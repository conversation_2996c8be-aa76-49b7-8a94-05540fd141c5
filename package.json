{"name": "pasabuy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:reset": "npx prisma migrate reset --force && npm run db:seed", "images:create": "node scripts/create-sample-images.js", "images:create-local": "node scripts/create-local-images.js", "icons:create": "node scripts/create-pwa-icons.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-visually-hidden": "^1.2.3", "@types/bcryptjs": "^2.4.6", "@types/xlsx": "^0.0.35", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "jose": "^6.0.11", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.0", "xlsx": "^0.18.5", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^6.8.2", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}