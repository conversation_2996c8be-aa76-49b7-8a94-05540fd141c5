/**
 * Test script for authentication system
 * Run with: node scripts/test-auth.js
 */

const BASE_URL = 'http://localhost:3000'

async function testAuth() {
  console.log('🔐 Testing Authentication System...\n')

  try {
    // Test 1: Register a new user
    console.log('1. Testing user registration...')
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        acceptTerms: true
      })
    })

    const registerData = await registerResponse.json()
    console.log('Registration response:', registerData)
    console.log('Status:', registerResponse.status)
    console.log('✅ Registration test completed\n')

    // Test 2: Login with the new user
    console.log('2. Testing user login...')
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPassword123!',
        rememberMe: false
      })
    })

    const loginData = await loginResponse.json()
    console.log('Login response:', loginData)
    console.log('Status:', loginResponse.status)
    
    if (loginData.token) {
      console.log('✅ Login successful, token received')
      
      // Test 3: Get current user info
      console.log('\n3. Testing get current user...')
      const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginData.token}`,
          'Content-Type': 'application/json',
        }
      })

      const meData = await meResponse.json()
      console.log('Me response:', meData)
      console.log('Status:', meResponse.status)
      console.log('✅ Get current user test completed\n')

      // Test 4: Logout
      console.log('4. Testing logout...')
      const logoutResponse = await fetch(`${BASE_URL}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${loginData.token}`,
          'Content-Type': 'application/json',
        }
      })

      const logoutData = await logoutResponse.json()
      console.log('Logout response:', logoutData)
      console.log('Status:', logoutResponse.status)
      console.log('✅ Logout test completed\n')

      // Test 5: Try to access protected endpoint after logout
      console.log('5. Testing access after logout (should fail)...')
      const afterLogoutResponse = await fetch(`${BASE_URL}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginData.token}`,
          'Content-Type': 'application/json',
        }
      })

      const afterLogoutData = await afterLogoutResponse.json()
      console.log('After logout response:', afterLogoutData)
      console.log('Status:', afterLogoutResponse.status)
      console.log('✅ Access after logout test completed\n')
    } else {
      console.log('❌ Login failed, no token received')
    }

    // Test 6: Login with admin user
    console.log('6. Testing admin login...')
    const adminLoginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password', // Default password from migration
        rememberMe: false
      })
    })

    const adminLoginData = await adminLoginResponse.json()
    console.log('Admin login response:', adminLoginData)
    console.log('Status:', adminLoginResponse.status)
    console.log('✅ Admin login test completed\n')

    console.log('🎉 All authentication tests completed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testAuth()
