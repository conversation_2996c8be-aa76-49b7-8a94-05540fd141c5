import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { withAdmin, withSecurityHeaders, combineMiddleware, AuthenticatedRequest } from '@/lib/auth-middleware'

const prisma = new PrismaClient()

// Get all roles (admin only)
async function handleGetRoles(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const roles = await prisma.role.findMany({
      where: { isActive: true },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: {
          where: { isActive: true },
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json({
      success: true,
      data: roles
    })
  } catch (error) {
    console.error('Get roles error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    )
  }
}

// Apply middleware
const getHandler = combineMiddleware(
  withSecurityHeaders,
  withAdmin
)(handleGetRoles)

export { getHandler as GET }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
