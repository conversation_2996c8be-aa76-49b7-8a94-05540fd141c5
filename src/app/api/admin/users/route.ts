import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { withAdmin, withSecurityHeaders, combineMiddleware, AuthenticatedRequest } from '@/lib/auth-middleware'
import { z } from 'zod'

const prisma = new PrismaClient()

// Get all users (admin only)
async function handleGetUsers(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''
    const status = searchParams.get('status') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status === 'active') {
      where.isActive = true
    } else if (status === 'inactive') {
      where.isActive = false
    }

    if (role) {
      where.roles = {
        some: {
          role: {
            name: role
          },
          isActive: true
        }
      }
    }

    // Get users with pagination
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          roles: {
            where: { isActive: true },
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ])

    // Remove sensitive data
    const sanitizedUsers = users.map(user => {
      const { passwordHash, emailVerificationToken, passwordResetToken, twoFactorSecret, ...sanitized } = user
      return sanitized
    })

    return NextResponse.json({
      success: true,
      data: {
        users: sanitizedUsers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get users error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// Create new user (admin only)
const createUserSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3).max(50).optional(),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  password: z.string().min(8),
  phone: z.string().optional(),
  timezone: z.string().default('Asia/Manila'),
  language: z.string().default('en'),
  isActive: z.boolean().default(true),
  emailVerified: z.boolean().default(false),
  roles: z.array(z.number()).optional()
})

async function handleCreateUser(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const body = await request.json()
    const validationResult = createUserSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { roles, password, ...userData } = validationResult.data

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: userData.email },
          ...(userData.username ? [{ username: userData.username }] : [])
        ]
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 409 }
      )
    }

    // Hash password
    const bcrypt = require('bcryptjs')
    const passwordHash = await bcrypt.hash(password, 12)

    // Create user
    const user = await prisma.user.create({
      data: {
        ...userData,
        passwordHash
      }
    })

    // Assign roles if provided
    if (roles && roles.length > 0) {
      await prisma.userRole.createMany({
        data: roles.map(roleId => ({
          userId: user.id,
          roleId,
          assignedBy: request.user!.id,
          assignedAt: new Date(),
          isActive: true
        }))
      })
    }

    // Create audit log
    await prisma.auditLog.create({
      data: {
        userId: request.user!.id,
        action: 'USER_CREATED',
        resource: 'users',
        resourceId: user.id.toString(),
        newValues: {
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          isActive: user.isActive
        }
      }
    })

    // Return created user (without sensitive data)
    const { passwordHash: _, emailVerificationToken, passwordResetToken, twoFactorSecret, ...sanitizedUser } = user

    return NextResponse.json({
      success: true,
      data: sanitizedUser
    }, { status: 201 })
  } catch (error) {
    console.error('Create user error:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

// Apply middleware
const getHandler = combineMiddleware(
  withSecurityHeaders,
  withAdmin
)(handleGetUsers)

const postHandler = combineMiddleware(
  withSecurityHeaders,
  withAdmin
)(handleCreateUser)

export { getHandler as GET, postHandler as POST }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
