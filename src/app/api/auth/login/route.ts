import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AuthService } from '@/lib/auth-service'
import { withRateLimit, withSecurityHeaders, combineMiddleware } from '@/lib/auth-middleware'

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional().default(false)
})

async function handleLogin(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json()

    // Validate input
    const validationResult = loginSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const loginData = validationResult.data

    // Get client IP and user agent for audit logging
    const ipAddress = request.ip || 
      request.headers.get('x-forwarded-for') || 
      request.headers.get('x-real-ip') || 
      'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Attempt login
    const result = await AuthService.login(loginData, ipAddress, userAgent)

    if (!result.success) {
      const statusCode = result.error === 'INVALID_CREDENTIALS' ? 401 :
                        result.error === 'ACCOUNT_DISABLED' ? 403 :
                        result.error === 'ACCOUNT_LOCKED' ? 423 : 500

      return NextResponse.json(
        {
          error: result.error,
          message: result.message
        },
        { status: statusCode }
      )
    }

    // Create response with tokens
    const response = NextResponse.json(
      {
        success: true,
        message: result.message,
        user: {
          id: result.user!.id,
          email: result.user!.email,
          username: result.user!.username,
          firstName: result.user!.firstName,
          lastName: result.user!.lastName,
          emailVerified: result.user!.emailVerified,
          isActive: result.user!.isActive,
          profilePicture: result.user!.profilePicture,
          phone: result.user!.phone,
          timezone: result.user!.timezone,
          language: result.user!.language,
          twoFactorEnabled: result.user!.twoFactorEnabled,
          lastLoginAt: result.user!.lastLoginAt,
          roles: result.user!.roles?.map(ur => ({
            name: ur.role.name,
            displayName: ur.role.displayName,
            permissions: ur.role.permissions.map(rp => ({
              name: rp.permission.name,
              resource: rp.permission.resource,
              action: rp.permission.action
            }))
          })) || []
        },
        token: result.token
      },
      { status: 200 }
    )

    // Set HTTP-only cookie for refresh token
    const cookieOptions = [
      `refreshToken=${result.refreshToken}`,
      'HttpOnly',
      'Secure',
      'SameSite=Strict',
      'Path=/',
      `Max-Age=${loginData.rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60}` // 30 days if remember me, 7 days otherwise
    ]

    response.headers.set('Set-Cookie', cookieOptions.join('; '))

    return response
  } catch (error) {
    console.error('Login endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware
const handler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 login attempts per 15 minutes per IP
    keyGenerator: (req) => req.ip || req.headers.get('x-forwarded-for') || 'anonymous'
  }, handler)
)(handleLogin)

export { handler as POST }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
