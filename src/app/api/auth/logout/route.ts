import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-service'
import { withAuth, withSecurityHeaders, combineMiddleware, AuthenticatedRequest } from '@/lib/auth-middleware'
import { JWTUtils } from '@/lib/auth'

async function handleLogout(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const user = request.user!

    // Get session ID from JWT token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')
    
    if (token) {
      const payload = await JWTUtils.verify(token)
      if (payload?.sessionId) {
        // Logout from specific session
        await AuthService.logout(payload.sessionId, user.id)
      }
    }

    // Create response
    const response = NextResponse.json(
      {
        success: true,
        message: 'Logout successful'
      },
      { status: 200 }
    )

    // Clear refresh token cookie
    response.headers.set(
      'Set-Cookie',
      'refreshToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0'
    )

    return response
  } catch (error) {
    console.error('Logout endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred during logout.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware
const handler = combineMiddleware(
  withSecurityHeaders,
  withAuth
)(handleLogout)

export { handler as POST }

// Handle logout all sessions
async function handleLogoutAll(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const user = request.user!

    // Logout from all sessions
    await AuthService.logout('', user.id) // Empty session ID will be handled in service

    // Create response
    const response = NextResponse.json(
      {
        success: true,
        message: 'Logged out from all devices'
      },
      { status: 200 }
    )

    // Clear refresh token cookie
    response.headers.set(
      'Set-Cookie',
      'refreshToken=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0'
    )

    return response
  } catch (error) {
    console.error('Logout all endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred during logout.'
      },
      { status: 500 }
    )
  }
}

// Export logout all as DELETE method
const logoutAllHandler = combineMiddleware(
  withSecurityHeaders,
  withAuth
)(handleLogoutAll)

export { logoutAllHandler as DELETE }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
