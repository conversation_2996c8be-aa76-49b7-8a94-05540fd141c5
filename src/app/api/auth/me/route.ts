import { NextRequest, NextResponse } from 'next/server'
import { withAuth, withSecurityHeaders, combineMiddleware, AuthenticatedRequest } from '@/lib/auth-middleware'

async function handleGetMe(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const user = request.user!

    // Return user information
    return NextResponse.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          emailVerified: user.emailVerified,
          isActive: user.isActive,
          profilePicture: user.profilePicture,
          phone: user.phone,
          timezone: user.timezone,
          language: user.language,
          twoFactorEnabled: user.twoFactorEnabled,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          roles: user.roles?.map(ur => ({
            id: ur.id,
            name: ur.role.name,
            displayName: ur.role.displayName,
            isActive: ur.isActive,
            expiresAt: ur.expiresAt,
            permissions: ur.role.permissions.map(rp => ({
              id: rp.permission.id,
              name: rp.permission.name,
              displayName: rp.permission.displayName,
              resource: rp.permission.resource,
              action: rp.permission.action
            }))
          })) || []
        }
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Get me endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while fetching user information.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware
const handler = combineMiddleware(
  withSecurityHeaders,
  withAuth
)(handleGetMe)

export { handler as GET }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
