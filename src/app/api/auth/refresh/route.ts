import { NextRequest, NextResponse } from 'next/server'
import { JWTUtils } from '@/lib/auth'
import { withRateLimit, withSecurityHeaders, combineMiddleware } from '@/lib/auth-middleware'

async function handleRefresh(request: NextRequest): Promise<NextResponse> {
  try {
    // Get refresh token from cookie
    const cookies = request.headers.get('cookie')
    const refreshToken = cookies
      ?.split(';')
      .find(c => c.trim().startsWith('refreshToken='))
      ?.split('=')[1]

    if (!refreshToken) {
      return NextResponse.json(
        {
          error: 'MISSING_REFRESH_TOKEN',
          message: 'Refresh token not found'
        },
        { status: 401 }
      )
    }

    // Refresh tokens
    const result = await JWTUtils.refresh(refreshToken)

    if (!result) {
      return NextResponse.json(
        {
          error: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token'
        },
        { status: 401 }
      )
    }

    // Create response with new tokens
    const response = NextResponse.json(
      {
        success: true,
        token: result.token,
        message: 'Tokens refreshed successfully'
      },
      { status: 200 }
    )

    // Set new refresh token cookie
    const cookieOptions = [
      `refreshToken=${result.refreshToken}`,
      'HttpOnly',
      'Secure',
      'SameSite=Strict',
      'Path=/',
      `Max-Age=${7 * 24 * 60 * 60}` // 7 days
    ]

    response.headers.set('Set-Cookie', cookieOptions.join('; '))

    return response
  } catch (error) {
    console.error('Token refresh endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred during token refresh.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware
const handler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20, // 20 refresh attempts per 15 minutes per IP
    keyGenerator: (req) => req.ip || req.headers.get('x-forwarded-for') || 'anonymous'
  }, handler)
)(handleRefresh)

export { handler as POST }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
