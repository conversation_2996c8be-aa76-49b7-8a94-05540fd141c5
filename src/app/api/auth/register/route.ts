import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AuthService } from '@/lib/auth-service'
import { withRateLimit, withSecurityHeaders, combineMiddleware } from '@/lib/auth-middleware'

// Validation schema
const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  username: z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username must be less than 50 characters').optional(),
  firstName: z.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters').optional(),
  lastName: z.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters').optional(),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  phone: z.string().optional(),
  timezone: z.string().optional(),
  language: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

async function handleRegister(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json()

    // Validate input
    const validationResult = registerSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { confirmPassword, acceptTerms, ...registerData } = validationResult.data

    // Get client IP and user agent for audit logging
    const ipAddress = request.ip || 
      request.headers.get('x-forwarded-for') || 
      request.headers.get('x-real-ip') || 
      'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Register user
    const result = await AuthService.register(registerData)

    if (!result.success) {
      const statusCode = result.error === 'USER_EXISTS' ? 409 : 
                        result.error === 'INVALID_PASSWORD' ? 400 : 500

      return NextResponse.json(
        {
          error: result.error,
          message: result.message
        },
        { status: statusCode }
      )
    }

    // Return success response (without sensitive data)
    return NextResponse.json(
      {
        success: true,
        message: result.message,
        user: {
          id: result.user!.id,
          email: result.user!.email,
          username: result.user!.username,
          firstName: result.user!.firstName,
          lastName: result.user!.lastName,
          emailVerified: result.user!.emailVerified,
          createdAt: result.user!.createdAt
        }
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Registration endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware
const handler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 registration attempts per 15 minutes
    keyGenerator: (req) => req.ip || req.headers.get('x-forwarded-for') || 'anonymous'
  }, handler)
)(handleRegister)

export { handler as POST }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
