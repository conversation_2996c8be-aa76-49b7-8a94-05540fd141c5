import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AuthService } from '@/lib/auth-service'
import { withRateLimit, withSecurityHeaders, combineMiddleware } from '@/lib/auth-middleware'

// Validation schemas
const requestResetSchema = z.object({
  email: z.string().email('Invalid email address')
})

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// Request password reset
async function handleRequestReset(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json()

    // Validate input
    const validationResult = requestResetSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { email } = validationResult.data

    // Request password reset
    const result = await AuthService.requestPasswordReset(email)

    return NextResponse.json(
      {
        success: result.success,
        message: result.message
      },
      { status: result.success ? 200 : 500 }
    )
  } catch (error) {
    console.error('Request password reset endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// Reset password with token
async function handleResetPassword(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json()

    // Validate input
    const validationResult = resetPasswordSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { token, password } = validationResult.data

    // Reset password
    const result = await AuthService.resetPassword(token, password)

    if (!result.success) {
      const statusCode = result.error === 'INVALID_TOKEN' ? 400 :
                        result.error === 'INVALID_PASSWORD' ? 400 : 500

      return NextResponse.json(
        {
          error: result.error,
          message: result.message
        },
        { status: statusCode }
      )
    }

    return NextResponse.json(
      {
        success: true,
        message: result.message
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Reset password endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware for POST (request reset)
const postHandler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 3, // 3 reset requests per 15 minutes per IP
    keyGenerator: (req) => req.ip || req.headers.get('x-forwarded-for') || 'anonymous'
  }, handler)
)(handleRequestReset)

// Apply middleware for PUT (reset password)
const putHandler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 reset attempts per 15 minutes per IP
    keyGenerator: (req) => req.ip || req.headers.get('x-forwarded-for') || 'anonymous'
  }, handler)
)(handleResetPassword)

export { postHandler as POST, putHandler as PUT }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
