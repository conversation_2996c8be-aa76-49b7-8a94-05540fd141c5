import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AuthService } from '@/lib/auth-service'
import { withRateLimit, withSecurityHeaders, combineMiddleware } from '@/lib/auth-middleware'

// Validation schema
const verifyEmailSchema = z.object({
  token: z.string().min(1, 'Verification token is required')
})

async function handleVerifyEmail(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json()

    // Validate input
    const validationResult = verifyEmailSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { token } = validationResult.data

    // Verify email
    const result = await AuthService.verifyEmail(token)

    if (!result.success) {
      const statusCode = result.error === 'INVALID_TOKEN' ? 400 : 500

      return NextResponse.json(
        {
          error: result.error,
          message: result.message
        },
        { status: statusCode }
      )
    }

    return NextResponse.json(
      {
        success: true,
        message: result.message
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Email verification endpoint error:', error)
    return NextResponse.json(
      {
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// Apply middleware
const handler = combineMiddleware(
  withSecurityHeaders,
  (handler) => withRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 verification attempts per 15 minutes per IP
    keyGenerator: (req) => req.ip || req.headers.get('x-forwarded-for') || 'anonymous'
  }, handler)
)(handleVerifyEmail)

export { handler as POST }

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
