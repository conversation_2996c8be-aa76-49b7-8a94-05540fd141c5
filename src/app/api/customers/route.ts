import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'
import { withPermission, AuthenticatedRequest } from '@/lib/auth-middleware'

async function handleGetCustomers(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const customers = await prisma.customer.findMany({
      include: {
        _count: {
          select: {
            orders: true,
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // Calculate additional counts and financial metrics for each customer
    const customersWithCounts = await Promise.all(
      customers.map(async (customer) => {
        const toBuyCount = await prisma.order.count({
          where: {
            customerId: customer.id,
            isBought: false
          }
        })

        const toPackCount = await prisma.order.count({
          where: {
            customerId: customer.id,
            isBought: true,
            packingStatus: 'Not Packed'
          }
        })

        // Calculate financial metrics from orders
        const orders = await prisma.order.findMany({
          where: { customerId: customer.id }
        })

        const totalSpent = orders.reduce((sum, order) => {
          return sum + (order.quantity * order.customerPrice)
        }, 0)

        const averageOrderValue = orders.length > 0 ? totalSpent / orders.length : 0

        return {
          ...customer,
          _count: {
            ...customer._count,
            toBuy: toBuyCount,
            toPack: toPackCount
          },
          totalSpent,
          averageOrderValue
        }
      })
    )

    return NextResponse.json(customersWithCounts)
  } catch (error) {
    console.error('Error fetching customers:', error)
    return NextResponse.json(
      { error: 'Error fetching customers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Customer name is required' },
        { status: 400 }
      )
    }

    const customer = await prisma.customer.create({
      data: {
        name: name.trim()
      }
    })

    return NextResponse.json(customer, { status: 201 })
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Customer name already exists' },
        { status: 400 }
      )
    }

    console.error('Error creating customer:', error)
    return NextResponse.json(
      { error: 'Error creating customer' },
      { status: 500 }
    )
  }
}
