import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'
import { withPermission, AuthenticatedRequest } from '@/lib/auth-middleware'
import { OrderTrackingService } from '@/lib/order-tracking'

async function handleGetOrder(
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: request.user!.id // Ensure user can only access their own orders
      } as any,
      include: {
        storeCode: true,
        customer: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(order)
  } catch (error) {
    console.error('Error fetching order:', error)
    return NextResponse.json(
      { error: 'Error fetching order' },
      { status: 500 }
    )
  }
}

async function handleUpdateOrder(
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      productName,
      quantity,
      usageUnit,
      comment,
      storePrice,
      pasabuyFee,
      customerPrice,
      storeCodeId,
      customerId,
      isBought,
      packingStatus,
      imageFilename
    } = body

    // Build update data object
    const updateData: Record<string, unknown> = {}

    if (productName !== undefined) updateData.productName = productName.trim()
    if (quantity !== undefined) updateData.quantity = parseInt(quantity)
    if (usageUnit !== undefined) updateData.usageUnit = usageUnit?.trim() || null
    if (comment !== undefined) updateData.comment = comment?.trim() || null
    if (storePrice !== undefined) updateData.storePrice = parseFloat(storePrice)
    if (pasabuyFee !== undefined) updateData.pasabuyFee = parseFloat(pasabuyFee)
    if (customerPrice !== undefined) updateData.customerPrice = parseFloat(customerPrice)
    if (storeCodeId !== undefined) updateData.storeCodeId = storeCodeId ? parseInt(storeCodeId) : null
    if (customerId !== undefined) updateData.customerId = customerId ? parseInt(customerId) : null
    if (isBought !== undefined) updateData.isBought = isBought
    if (packingStatus !== undefined) updateData.packingStatus = packingStatus
    if (imageFilename !== undefined) updateData.imageFilename = imageFilename

    // Auto-calculate customer price if store price or pasabuy fee changed
    if ((storePrice !== undefined || pasabuyFee !== undefined) && customerPrice === undefined) {
      const currentOrder = await prisma.order.findUnique({ where: { id: orderId } })
      if (currentOrder) {
        const newStorePrice = storePrice !== undefined ? parseFloat(storePrice) : currentOrder.storePrice
        const newPasabuyFee = pasabuyFee !== undefined ? parseFloat(pasabuyFee) : currentOrder.pasabuyFee
        updateData.customerPrice = Number(newStorePrice) + Number(newPasabuyFee)
      }
    }

    // Handle order tracking before update
    await OrderTrackingService.handleOrderUpdate(orderId, updateData, {
      changedBy: 'system', // TODO: Replace with actual user ID when auth is implemented
      changeReason: 'Manual update via API'
    })

    // First check if order exists and belongs to user
    const existingOrder = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: request.user!.id
      } as any
    })

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    const order = await prisma.order.update({
      where: { id: orderId },
      data: updateData,
      include: {
        storeCode: true,
        customer: true
      }
    })

    // Note: Immediate auto-invoice generation disabled to prevent invoice fragmentation
    // Daily batching system will handle invoice generation at end of day
    // Manual generation is still available through the packing interface

    return NextResponse.json(order)
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    console.error('Error updating order:', error)
    return NextResponse.json(
      { error: 'Error updating order' },
      { status: 500 }
    )
  }
}

async function handleDeleteOrder(
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    // First check if order exists and belongs to user
    const existingOrder = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: request.user!.id
      } as any
    })

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    await prisma.order.delete({
      where: { id: orderId }
    })

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    console.error('Error deleting order:', error)
    return NextResponse.json(
      { error: 'Error deleting order' },
      { status: 500 }
    )
  }
}

// Apply middleware and export handlers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const handler = withPermission(
    { resource: 'orders', action: 'read' },
    (req: AuthenticatedRequest) => handleGetOrder(req, { params })
  )
  return handler(request)
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const handler = withPermission(
    { resource: 'orders', action: 'update' },
    (req: AuthenticatedRequest) => handleUpdateOrder(req, { params })
  )
  return handler(request)
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const handler = withPermission(
    { resource: 'orders', action: 'delete' },
    (req: AuthenticatedRequest) => handleDeleteOrder(req, { params })
  )
  return handler(request)
}
