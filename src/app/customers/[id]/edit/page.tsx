'use client'

import { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import { CustomerForm } from '@/components/forms/customer-form'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { LuArrowLeft } from 'react-icons/lu'

type Customer = {
  id: number
  name: string
  customerNumber?: string
  customerType: string
  status: string
  segment: string
  loyaltyTier: string
  email?: string
  phone?: string
  alternatePhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  businessName?: string
  taxId?: string
  businessType?: string
  preferredDeliveryMethod?: string
  preferredPaymentMethod?: string
  creditLimit: number
  paymentTerms: number
  discountRate: number
  assignedSalesRep?: string
  accountManager?: string
  referredBy?: string
  notes?: string
  internalNotes?: string
}

export default function EditCustomerPage() {
  const params = useParams()
  const router = useRouter()
  const customerId = parseInt(params.id as string)
  
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchCustomer() {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/customers/${customerId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch customer')
        }

        const customerData = await response.json()
        setCustomer(customerData)
      } catch (err) {
        console.error('Error fetching customer:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch customer')
      } finally {
        setIsLoading(false)
      }
    }

    if (customerId) {
      fetchCustomer()
    }
  }, [customerId])

  const handleSubmit = async (data: Record<string, unknown>) => {
    try {
      setIsSubmitting(true)
      setError(null)

      const response = await fetch(`/api/customers/${customerId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update customer')
      }

      // Redirect back to customer detail page with refresh parameter
      router.push(`/customers/${customerId}?refresh=true`)
    } catch (err) {
      console.error('Error updating customer:', err)
      setError(err instanceof Error ? err.message : 'Failed to update customer')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.push(`/customers/${customerId}`)
  }

  if (isLoading) {
    return (
      <SimplePageWrapper title="Edit Customer" description="Update customer information">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-muted-foreground mt-2">Loading customer...</p>
          </div>
        </div>
      </SimplePageWrapper>
    )
  }

  if (error) {
    return (
      <SimplePageWrapper title="Edit Customer" description="Update customer information">
        <Card className="p-6">
          <div className="text-center">
            <h3 className="text-sm font-medium mt-2">Error loading customer</h3>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <div className="flex gap-2 justify-center mt-4">
              <Button onClick={() => window.location.reload()} size="sm">
                Try Again
              </Button>
              <Button variant="outline" onClick={handleCancel} size="sm">
                Go Back
              </Button>
            </div>
          </div>
        </Card>
      </SimplePageWrapper>
    )
  }

  if (!customer) {
    return (
      <SimplePageWrapper title="Edit Customer" description="Update customer information">
        <Card className="p-6">
          <div className="text-center">
            <h3 className="text-sm font-medium mt-2">Customer not found</h3>
            <p className="text-sm text-muted-foreground mt-1">
              The customer you're looking for doesn't exist or has been deleted.
            </p>
            <Button variant="outline" onClick={handleCancel} size="sm" className="mt-4">
              Go Back
            </Button>
          </div>
        </Card>
      </SimplePageWrapper>
    )
  }

  return (
    <SimplePageWrapper title="Edit Customer" description="Update customer information">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={handleCancel}
          >
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">Edit Customer</h1>
            <p className="text-muted-foreground">
              Update information for {customer.name}
            </p>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="p-4 border-destructive">
            <div className="text-sm text-destructive">
              <strong>Error:</strong> {error}
            </div>
          </Card>
        )}

        {/* Customer Form */}
        <CustomerForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          initialData={customer}
          isLoading={isSubmitting}
        />
      </div>
    </SimplePageWrapper>
  )
}
