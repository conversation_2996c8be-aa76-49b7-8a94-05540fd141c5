'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import { Pagination } from '@/components/ui/pagination'
import Link from 'next/link'
import { LuUser, LuSearch, LuFilter, LuPlus } from 'react-icons/lu'
import { EnhancedCustomerCard } from '@/components/customers/enhanced-customer-card'
import { CustomerFilters } from '@/components/customers/customer-filters'



type EnhancedCustomer = {
  id: number
  name: string
  customerNumber?: string
  customerType: string
  status: string
  segment: string
  loyaltyTier: string
  email?: string
  phone?: string
  city?: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  lastOrderDate?: string
  _count?: {
    orders: number
    toBuy: number
    toPack: number
  }
}

type CustomerFilters = {
  searchTerm: string
  customerType: string[]
  status: string[]
  segment: string[]
  loyaltyTier: string[]
  city: string
  totalSpentMin: string
  totalSpentMax: string
  hasOrders: boolean | null
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<EnhancedCustomer[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<CustomerFilters>({
    searchTerm: '',
    customerType: [],
    status: [],
    segment: [],
    loyaltyTier: [],
    city: '',
    totalSpentMin: '',
    totalSpentMax: '',
    hasOrders: null
  })

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 20

  // Fetch customers with enhanced search and filtering
  const fetchCustomers = async (page = 1, searchFilters = filters) => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: page.toString(),
        limit: itemsPerPage.toString(),
        sortField: 'name',
        sortDirection: 'asc'
      })

      // Add search term
      if (searchTerm.trim()) {
        params.append('searchTerm', searchTerm.trim())
      }

      // Add filters
      if (searchFilters.customerType.length > 0) {
        params.append('customerType', searchFilters.customerType.join(','))
      }
      if (searchFilters.status.length > 0) {
        params.append('status', searchFilters.status.join(','))
      }
      if (searchFilters.segment.length > 0) {
        params.append('segment', searchFilters.segment.join(','))
      }
      if (searchFilters.loyaltyTier.length > 0) {
        params.append('loyaltyTier', searchFilters.loyaltyTier.join(','))
      }
      if (searchFilters.city.trim()) {
        params.append('city', searchFilters.city.trim())
      }
      if (searchFilters.totalSpentMin.trim()) {
        params.append('totalSpentMin', searchFilters.totalSpentMin.trim())
      }
      if (searchFilters.totalSpentMax.trim()) {
        params.append('totalSpentMax', searchFilters.totalSpentMax.trim())
      }
      if (searchFilters.hasOrders !== null) {
        params.append('hasOrders', searchFilters.hasOrders.toString())
      }

      const response = await fetch(`/api/customers?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch customers')
      }

      const data = await response.json()
      setCustomers(data.customers || [])
      setTotalPages(Math.ceil((data.total || 0) / itemsPerPage))
    } catch (err) {
      console.error('Error fetching customers:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch customers')
      setCustomers([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCustomers(currentPage, filters)
  }, [currentPage]) // eslint-disable-line react-hooks/exhaustive-deps

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1)
    const newFilters = { ...filters, searchTerm: term }
    setFilters(newFilters)
    fetchCustomers(1, newFilters)
  }

  // Handle filter changes
  const handleFiltersChange = (newFilters: Partial<CustomerFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    setCurrentPage(1)
    fetchCustomers(1, updatedFilters)
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // Get loyalty tier color
  const getLoyaltyTierColor = (tier: string) => {
    switch (tier) {
      case 'DIAMOND': return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'PLATINUM': return 'text-gray-600 bg-gray-50 border-gray-200'
      case 'GOLD': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'SILVER': return 'text-gray-500 bg-gray-50 border-gray-200'
      case 'BRONZE': return 'text-orange-600 bg-orange-50 border-orange-200'
      default: return 'text-gray-500 bg-gray-50 border-gray-200'
    }
  }

  // Get customer status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 bg-green-50 border-green-200'
      case 'INACTIVE': return 'text-gray-500 bg-gray-50 border-gray-200'
      case 'SUSPENDED': return 'text-red-600 bg-red-50 border-red-200'
      case 'PROSPECT': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-500 bg-gray-50 border-gray-200'
    }
  }





  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0
    if (filters.customerType.length > 0) count++
    if (filters.status.length > 0) count++
    if (filters.segment.length > 0) count++
    if (filters.loyaltyTier.length > 0) count++
    if (filters.city.trim()) count++
    if (filters.totalSpentMin.trim() || filters.totalSpentMax.trim()) count++
    if (filters.hasOrders !== null) count++
    return count
  }



  if (error) {
    return (
      <SimplePageWrapper title="Customers">
        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-base font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-2">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Try Again
            </button>
          </div>
        </Card>
      </SimplePageWrapper>
    )
  }

  return (
    <SimplePageWrapper title="Customers">

      {isLoading ? (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-4 h-20 flex items-center">
              <div className="animate-pulse w-full">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
                    </div>
                  </div>
                  <div className="h-5 w-5 bg-gray-200 rounded flex-shrink-0"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : customers.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <LuUser className="mx-auto h-12 w-12 text-gray-400" />
            <h2 className="text-base font-medium mt-2">No active customers</h2>
            <p className="text-sm text-muted-foreground mt-2">
              {customers.length === 0
                ? "Add some customers to get started with your business."
                : "All customers have completed their orders! Add new orders to continue."
              }
            </p>
            <p className="text-xs text-muted-foreground mt-3">Use the + button below to add orders or customers.</p>
          </div>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Search and Filter Header */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <LuFilter className="h-4 w-4" />
                Filters
                {getActiveFilterCount() > 0 && (
                  <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 text-xs">
                    {getActiveFilterCount()}
                  </Badge>
                )}
              </Button>
              <Link href="/customers/new">
                <Button className="flex items-center gap-2">
                  <LuPlus className="h-4 w-4" />
                  Add Customer
                </Button>
              </Link>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <CustomerFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClose={() => setShowFilters(false)}
            />
          )}

          {/* Enhanced Customer Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {customers.map((customer) => (
              <EnhancedCustomerCard
                key={customer.id}
                customer={customer}
                getLoyaltyTierColor={getLoyaltyTierColor}
                getStatusColor={getStatusColor}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      )}
    </SimplePageWrapper>
  )
}
