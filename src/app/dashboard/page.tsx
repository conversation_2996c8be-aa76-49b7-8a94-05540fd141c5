'use client'

import { ProtectedRoute } from '@/components/auth/protected-route'
import { useAuth } from '@/contexts/auth-context'
import { LogOut, User, Shield, Settings } from 'lucide-react'

function DashboardContent() {
  const { user, logout, hasRole, hasPermission } = useAuth()

  const handleLogout = async () => {
    await logout()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">PasaBuy Local Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                Welcome, {user?.firstName || user?.email}
              </span>
              <button
                onClick={handleLogout}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* User Info Card */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <User className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        User Information
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {user?.firstName} {user?.lastName}
                      </dd>
                      <dd className="text-sm text-gray-500">
                        {user?.email}
                      </dd>
                      <dd className="text-sm text-gray-500">
                        {user?.emailVerified ? (
                          <span className="text-green-600">Email Verified</span>
                        ) : (
                          <span className="text-red-600">Email Not Verified</span>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Roles Card */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Shield className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Your Roles
                      </dt>
                      <dd className="mt-1">
                        {user?.roles.map((role) => (
                          <span
                            key={role.id}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2 mb-1"
                          >
                            {role.displayName}
                          </span>
                        ))}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions Card */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Settings className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Quick Actions
                      </dt>
                      <dd className="mt-2 space-y-2">
                        {hasPermission('orders', 'create') && (
                          <button className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded">
                            Create Order
                          </button>
                        )}
                        {hasPermission('customers', 'create') && (
                          <button className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded">
                            Add Customer
                          </button>
                        )}
                        {hasRole('admin') && (
                          <button className="block w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded">
                            Admin Panel
                          </button>
                        )}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Permissions Debug Info */}
          <div className="mt-8 bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Your Permissions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {user?.roles.map((role) => (
                  <div key={role.id} className="border rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">{role.displayName}</h4>
                    <div className="space-y-1">
                      {role.permissions.map((permission) => (
                        <div key={permission.id} className="text-sm text-gray-600">
                          {permission.displayName}
                          <span className="text-xs text-gray-400 ml-1">
                            ({permission.resource}.{permission.action})
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}
