import type { <PERSON>ada<PERSON>, Viewport } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import { AnimatedBottomNav } from '@/components/navigation/animated-bottom-nav'
import { FloatingActionButton } from '@/components/navigation/floating-action-button'
import { TopNav } from '@/components/navigation/top-nav'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { AuthProvider } from '@/contexts/auth-context'
import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Pasabuy Pal',
  description: 'A self-hosted PWA for managing pasabuy orders',
  manifest: '/manifest.json',
}

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased flex min-h-screen flex-col`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <TopNav />
            <main className="flex-1 container mx-auto px-4 pb-16 pt-4">
              {children}
            </main>
            <AnimatedBottomNav />
            <FloatingActionButton />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
