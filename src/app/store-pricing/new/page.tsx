'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useScrollToTop } from '@/hooks/use-scroll'
import { LuArrowLeft, LuPlus, LuLoader } from 'react-icons/lu'

interface StoreCode {
  id: number
  code: string
  name: string | null
}

export default function NewStorePricingPage() {
  const router = useRouter()

  const [stores, setStores] = useState<StoreCode[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [selectedStoreId, setSelectedStoreId] = useState('')
  const [name, setName] = useState('')
  const [markupType, setMarkupType] = useState<'PERCENTAGE' | 'FIXED_AMOUNT'>('PERCENTAGE')
  const [markupValue, setMarkupValue] = useState('')
  const [serviceFee, setServiceFee] = useState('')

  useScrollToTop()

  // Hide bottom navigation for focused experience
  useEffect(() => {
    document.body.style.paddingBottom = '0'
    return () => {
      document.body.style.paddingBottom = '4rem'
    }
  }, [])

  useEffect(() => {
    fetchStores()
  }, [])

  const fetchStores = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/store-codes')
      
      if (!response.ok) {
        throw new Error('Failed to fetch stores')
      }
      
      const data = await response.json()
      setStores(data)
    } catch (error) {
      console.error('Error fetching stores:', error)
      setError('Failed to load stores')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedStoreId) {
      setError('Please select a store')
      return
    }

    if (!name.trim()) {
      setError('Pricing configuration name is required')
      return
    }

    const markupVal = parseFloat(markupValue)
    const serviceFeeVal = parseFloat(serviceFee)

    if (isNaN(markupVal) || markupVal < 0) {
      setError('Markup value must be a valid non-negative number')
      return
    }

    if (isNaN(serviceFeeVal) || serviceFeeVal < 0) {
      setError('Service fee must be a valid non-negative number')
      return
    }

    try {
      setSaving(true)
      setError(null)

      const response = await fetch('/api/store-pricing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storeCodeId: parseInt(selectedStoreId),
          name: name.trim(),
          markupType,
          markupValue: markupVal,
          serviceFee: serviceFeeVal
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create store pricing')
      }

      router.push('/store-pricing')
    } catch (error) {
      console.error('Error creating store pricing:', error)
      setError(error instanceof Error ? error.message : 'Failed to create store pricing')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    router.push('/store-pricing')
  }

  // Auto-generate name when store is selected
  useEffect(() => {
    if (selectedStoreId && !name) {
      const selectedStore = stores.find(s => s.id.toString() === selectedStoreId)
      if (selectedStore) {
        setName(`${selectedStore.code} Custom Pricing`)
      }
    }
  }, [selectedStoreId, stores, name])

  // Calculate example pricing
  const exampleStorePrice = 500
  const markupVal = parseFloat(markupValue) || 0
  const serviceFeeVal = parseFloat(serviceFee) || 0
  
  const exampleMarkup = markupType === 'PERCENTAGE' 
    ? exampleStorePrice * (markupVal / 100)
    : markupVal
  const exampleCustomerPrice = exampleStorePrice + exampleMarkup + serviceFeeVal

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center py-8">
          <LuLoader className="h-6 w-6 animate-spin mr-2" />
          <span>Loading stores...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleCancel}
          className="h-10 w-10"
        >
          <LuArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Create Store Pricing</h1>
          <p className="text-muted-foreground">
            Configure store-specific pricing rules and fees
          </p>
        </div>
      </div>

      <div className="grid gap-6 max-w-2xl">
        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing Configuration</CardTitle>
            <CardDescription>
              Set up custom pricing for a specific store
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Store Selection */}
              <div className="space-y-2">
                <Label htmlFor="store">Store</Label>
                <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
                  <SelectTrigger className="min-h-[44px]">
                    <SelectValue placeholder="Select a store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.map((store) => (
                      <SelectItem key={store.id} value={store.id.toString()}>
                        {store.code} {store.name && `(${store.name})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Configuration Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Configuration Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="e.g., Premium Store Pricing"
                  className="min-h-[44px]"
                  required
                />
              </div>

              {/* Markup Type */}
              <div className="space-y-2">
                <Label htmlFor="markupType">Markup Type</Label>
                <Select value={markupType} onValueChange={(value: 'PERCENTAGE' | 'FIXED_AMOUNT') => setMarkupType(value)}>
                  <SelectTrigger className="min-h-[44px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PERCENTAGE">Percentage (%)</SelectItem>
                    <SelectItem value="FIXED_AMOUNT">Fixed Amount (₱)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Markup Value */}
              <div className="space-y-2">
                <Label htmlFor="markupValue">
                  Markup Value {markupType === 'PERCENTAGE' ? '(%)' : '(₱)'}
                </Label>
                <Input
                  id="markupValue"
                  type="number"
                  step="0.01"
                  min="0"
                  value={markupValue}
                  onChange={(e) => setMarkupValue(e.target.value)}
                  placeholder={markupType === 'PERCENTAGE' ? '100' : '150.00'}
                  className="min-h-[44px]"
                  required
                />
              </div>

              {/* Service Fee */}
              <div className="space-y-2">
                <Label htmlFor="serviceFee">Service Fee (₱)</Label>
                <Input
                  id="serviceFee"
                  type="number"
                  step="0.01"
                  min="0"
                  value={serviceFee}
                  onChange={(e) => setServiceFee(e.target.value)}
                  placeholder="20.00"
                  className="min-h-[44px]"
                  required
                />
              </div>

              {/* Error Display */}
              {error && (
                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded text-destructive text-sm">
                  {error}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="submit"
                  disabled={saving}
                  className="min-h-[44px] flex-1"
                >
                  {saving ? (
                    <>
                      <LuLoader className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <LuPlus className="h-4 w-4 mr-2" />
                      Create Store Pricing
                    </>
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={saving}
                  className="min-h-[44px]"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Example Calculation */}
        {markupValue && serviceFee && (
          <Card>
            <CardHeader>
              <CardTitle>Example Calculation</CardTitle>
              <CardDescription>
                Preview how pricing will be calculated with these settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Store Price:</span>
                  <span>₱{exampleStorePrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>
                    Markup ({markupType === 'PERCENTAGE' ? `${markupVal}%` : `₱${markupVal}`}):
                  </span>
                  <span className="text-green-600">+₱{exampleMarkup.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Service Fee:</span>
                  <span className="text-blue-600">+₱{serviceFeeVal.toFixed(2)}</span>
                </div>
                <hr />
                <div className="flex justify-between font-medium">
                  <span>Customer Price:</span>
                  <span>₱{exampleCustomerPrice.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
