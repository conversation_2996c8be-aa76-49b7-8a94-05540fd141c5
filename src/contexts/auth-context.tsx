'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'

export interface User {
  id: number
  email: string
  username?: string | null
  firstName?: string | null
  lastName?: string | null
  emailVerified: boolean
  isActive: boolean
  profilePicture?: string | null
  phone?: string | null
  timezone: string
  language: string
  twoFactorEnabled: boolean
  lastLoginAt?: Date | null
  createdAt: Date
  updatedAt: Date
  roles: UserRole[]
}

export interface UserRole {
  id: number
  name: string
  displayName: string
  isActive: boolean
  expiresAt?: Date | null
  permissions: Permission[]
}

export interface Permission {
  id: number
  name: string
  displayName: string
  resource: string
  action: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  refreshToken: () => Promise<boolean>
  hasPermission: (resource: string, action: string) => boolean
  hasRole: (role: string) => boolean
  updateUser: (userData: Partial<User>) => void
}

export interface RegisterData {
  email: string
  username?: string
  firstName?: string
  lastName?: string
  password: string
  confirmPassword: string
  phone?: string
  timezone?: string
  language?: string
  acceptTerms: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    isAuthenticated: false
  })

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token')
        if (token) {
          // Verify token and get user info
          const response = await fetch('/api/auth/me', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          })

          if (response.ok) {
            const data = await response.json()
            setState({
              user: data.user,
              token,
              isLoading: false,
              isAuthenticated: true
            })
          } else {
            // Token is invalid, remove it
            localStorage.removeItem('auth_token')
            setState({
              user: null,
              token: null,
              isLoading: false,
              isAuthenticated: false
            })
          }
        } else {
          setState({
            user: null,
            token: null,
            isLoading: false,
            isAuthenticated: false
          })
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        setState({
          user: null,
          token: null,
          isLoading: false,
          isAuthenticated: false
        })
      }
    }

    initializeAuth()
  }, [])

  // Auto-refresh token
  useEffect(() => {
    if (state.isAuthenticated && state.token) {
      const interval = setInterval(async () => {
        await refreshToken()
      }, 5 * 60 * 1000) // Refresh every 5 minutes

      return () => clearInterval(interval)
    }
  }, [state.isAuthenticated, state.token])

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password, rememberMe })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        localStorage.setItem('auth_token', data.token)
        setState({
          user: data.user,
          token: data.token,
          isLoading: false,
          isAuthenticated: true
        })
        return { success: true }
      } else {
        return { success: false, error: data.message || 'Login failed' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error occurred' }
    }
  }

  const register = async (data: RegisterData) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        return { success: true }
      } else {
        return { success: false, error: result.message || 'Registration failed' }
      }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: 'Network error occurred' }
    }
  }

  const logout = async () => {
    try {
      if (state.token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${state.token}`,
            'Content-Type': 'application/json'
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('auth_token')
      setState({
        user: null,
        token: null,
        isLoading: false,
        isAuthenticated: false
      })
    }
  }

  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        localStorage.setItem('auth_token', data.token)
        setState(prev => ({
          ...prev,
          token: data.token
        }))
        return true
      } else {
        // Refresh failed, logout user
        await logout()
        return false
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      await logout()
      return false
    }
  }

  const hasPermission = (resource: string, action: string): boolean => {
    if (!state.user || !state.isAuthenticated) return false

    return state.user.roles.some(role =>
      role.permissions.some(permission =>
        permission.resource === resource && permission.action === action
      )
    )
  }

  const hasRole = (roleName: string): boolean => {
    if (!state.user || !state.isAuthenticated) return false

    return state.user.roles.some(role => role.name === roleName)
  }

  const updateUser = (userData: Partial<User>) => {
    setState(prev => ({
      ...prev,
      user: prev.user ? { ...prev.user, ...userData } : null
    }))
  }

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshToken,
    hasPermission,
    hasRole,
    updateUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
