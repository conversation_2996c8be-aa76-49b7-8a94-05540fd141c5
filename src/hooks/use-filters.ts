import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { FilterConfig, SortConfig, FilterResponse } from '@/lib/filter-types'

interface UseFiltersOptions {
  debounceMs?: number
  autoApply?: boolean
}

interface UseFiltersReturn {
  // Filter state
  filters: FilterConfig
  sort: SortConfig
  isLoading: boolean
  error: string | null

  // Data
  items: unknown[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  } | null

  // Actions
  updateFilter: (key: keyof FilterConfig, value: unknown) => void
  updateSort: (sort: SortConfig) => void
  updatePagination: (page: number, limit?: number) => void
  clearFilters: () => void
  applyFilters: () => void


}

const defaultPagination = {
  page: 1,
  limit: 15,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrev: false
}

const defaultSort: SortConfig = {
  columns: [
    {
      field: 'createdAt',
      direction: 'desc',
      priority: 0
    }
  ]
}

export function useFilters(options: UseFiltersOptions = {}): UseFiltersReturn {
  const { debounceMs = 500, autoApply = true } = options
  const router = useRouter()
  const searchParams = useSearchParams()

  // State
  const [filters, setFilters] = useState<FilterConfig>({})
  const [sort, setSort] = useState<SortConfig>(defaultSort)
  const [currentPagination, setCurrentPagination] = useState(defaultPagination)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [items, setItems] = useState<unknown[]>([])
  const [pagination, setPagination] = useState(defaultPagination)

  const [debouncedFilters, setDebouncedFilters] = useState<FilterConfig>({})

  // Debounce filters
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [filters, debounceMs])

  // Apply filters when debounced filters change
  useEffect(() => {
    if (!autoApply) return

    const performFiltering = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Build query parameters inline to avoid dependency issues
        const params = new URLSearchParams()

        // Text filters
        if (debouncedFilters.productName) {
          params.set('productName.value', debouncedFilters.productName.value || '')
          params.set('productName.operator', debouncedFilters.productName.operator || 'contains')
          if (debouncedFilters.productName.caseSensitive !== undefined) {
            params.set('productName.caseSensitive', debouncedFilters.productName.caseSensitive.toString())
          }
        }

        // Legacy search support
        if (debouncedFilters.search) {
          params.set('search', debouncedFilters.search)
        }

        // Status filters
        if (debouncedFilters.isBought !== undefined && debouncedFilters.isBought !== null) {
          params.set('isBought.value', debouncedFilters.isBought.toString())
          params.set('isBought.operator', 'equals')
        }

        if (debouncedFilters.packingStatus && debouncedFilters.packingStatus.length > 0) {
          params.set('packingStatus.value', debouncedFilters.packingStatus.join(','))
          params.set('packingStatus.operator', 'in')
        }

        // Entity filters
        if (debouncedFilters.customers) {
          if (debouncedFilters.customers.include.length > 0) {
            params.set('customers.include', debouncedFilters.customers.include.join(','))
          }
          if (debouncedFilters.customers.exclude.length > 0) {
            params.set('customers.exclude', debouncedFilters.customers.exclude.join(','))
          }
          if (debouncedFilters.customers.includeNull) {
            params.set('customers.includeNull', 'true')
          }
        }

        if (debouncedFilters.storeCodes) {
          if (debouncedFilters.storeCodes.include.length > 0) {
            params.set('storeCodes.include', debouncedFilters.storeCodes.include.join(','))
          }
          if (debouncedFilters.storeCodes.exclude.length > 0) {
            params.set('storeCodes.exclude', debouncedFilters.storeCodes.exclude.join(','))
          }
          if (debouncedFilters.storeCodes.includeNull) {
            params.set('storeCodes.includeNull', 'true')
          }
        }

        // Sort parameters
        if (sort.columns.length > 0) {
          sort.columns.forEach((column, index) => {
            params.set(`sort.${index}.field`, column.field)
            params.set(`sort.${index}.direction`, column.direction)
            params.set(`sort.${index}.priority`, column.priority.toString())
          })
        }

        // Pagination parameters
        params.set('page', currentPagination.page.toString())
        params.set('limit', currentPagination.limit.toString())

        const queryParams = params.toString()

        // Update URL
        const newUrl = queryParams ? `?${queryParams}` : ''
        router.replace(newUrl, { scroll: false })

        // Fetch data
        const response = await fetch(`/api/orders?${queryParams}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          cache: 'no-cache'
        })

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`Failed to fetch orders: ${response.status} ${response.statusText} - ${errorText}`)
        }

        const data = await response.json()

        // Handle both legacy and advanced response formats
        if (Array.isArray(data)) {
          // Legacy format
          setItems(data)
          setPagination({
            ...defaultPagination,
            total: data.length
          })
        } else {
          // Advanced format
          const filterResponse = data as FilterResponse<unknown>
          setItems(filterResponse.data)
          setPagination(filterResponse.pagination)
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setItems([])
        setPagination(defaultPagination)
      } finally {
        setIsLoading(false)
      }
    }

    performFiltering()
  }, [debouncedFilters, sort, currentPagination, autoApply, router])

  // Initialize from URL parameters
  useEffect(() => {
    const urlFilters: FilterConfig = {}
    const urlSort: SortConfig = { columns: [] }
    const urlPagination = { ...defaultPagination }

    // Parse basic filters from URL
    const isBought = searchParams.get('isBought')
    if (isBought !== null) {
      if (isBought === 'true') urlFilters.isBought = true
      else if (isBought === 'false') urlFilters.isBought = false
    }

    const search = searchParams.get('search')
    if (search) {
      urlFilters.search = search
    }

    const storeCodeId = searchParams.get('storeCodeId')
    if (storeCodeId) {
      urlFilters.storeCodes = {
        include: [parseInt(storeCodeId)],
        exclude: []
      }
    }

    const customerId = searchParams.get('customerId')
    if (customerId) {
      urlFilters.customers = {
        include: [parseInt(customerId)],
        exclude: []
      }
    }

    // Parse sort from URL
    const sortField = searchParams.get('sort')
    const sortDir = searchParams.get('sortDir')
    if (sortField && sortDir) {
      urlSort.columns = [{
        field: sortField,
        direction: sortDir as 'asc' | 'desc',
        priority: 0
      }]
    }

    // Parse pagination from URL
    const page = searchParams.get('page')
    const limit = searchParams.get('limit')
    if (page) {
      const pageNum = parseInt(page)
      if (!isNaN(pageNum) && pageNum > 0) {
        urlPagination.page = pageNum
      }
    }
    if (limit) {
      const limitNum = parseInt(limit)
      if (!isNaN(limitNum) && limitNum > 0 && limitNum <= 100) {
        urlPagination.limit = limitNum
      }
    }

    if (Object.keys(urlFilters).length > 0) {
      setFilters(urlFilters)
    }
    if (urlSort.columns.length > 0) {
      setSort(urlSort)
    }
    if (urlPagination.page !== defaultPagination.page || urlPagination.limit !== defaultPagination.limit) {
      setCurrentPagination(urlPagination)
    }
  }, [searchParams])

  const buildQueryParams = useCallback((filters: FilterConfig, sort: SortConfig, paginationConfig = currentPagination) => {
    const params = new URLSearchParams()

    // Text filters
    if (filters.productName) {
      params.set('productName.value', filters.productName.value || '')
      params.set('productName.operator', filters.productName.operator || 'contains')
      if (filters.productName.caseSensitive !== undefined) {
        params.set('productName.caseSensitive', filters.productName.caseSensitive.toString())
      }
    }

    // Legacy search support
    if (filters.search) {
      params.set('search', filters.search)
    }

    // Status filters
    if (filters.isBought !== undefined && filters.isBought !== null) {
      params.set('isBought.value', filters.isBought.toString())
      params.set('isBought.operator', 'equals')
    }

    if (filters.packingStatus && filters.packingStatus.length > 0) {
      params.set('packingStatus.value', filters.packingStatus.join(','))
      params.set('packingStatus.operator', 'in')
    }

    // Number filters
    if (filters.storePrice) {
      params.set('storePrice.operator', filters.storePrice.operator)
      if (filters.storePrice.value !== undefined) {
        params.set('storePrice.value', filters.storePrice.value.toString())
      }
      if (filters.storePrice.min !== undefined) {
        params.set('storePrice.min', filters.storePrice.min.toString())
      }
      if (filters.storePrice.max !== undefined) {
        params.set('storePrice.max', filters.storePrice.max.toString())
      }
    }

    if (filters.pasabuyFee) {
      params.set('pasabuyFee.operator', filters.pasabuyFee.operator)
      if (filters.pasabuyFee.value !== undefined) {
        params.set('pasabuyFee.value', filters.pasabuyFee.value.toString())
      }
      if (filters.pasabuyFee.min !== undefined) {
        params.set('pasabuyFee.min', filters.pasabuyFee.min.toString())
      }
      if (filters.pasabuyFee.max !== undefined) {
        params.set('pasabuyFee.max', filters.pasabuyFee.max.toString())
      }
    }

    if (filters.customerPrice) {
      params.set('customerPrice.operator', filters.customerPrice.operator)
      if (filters.customerPrice.value !== undefined) {
        params.set('customerPrice.value', filters.customerPrice.value.toString())
      }
      if (filters.customerPrice.min !== undefined) {
        params.set('customerPrice.min', filters.customerPrice.min.toString())
      }
      if (filters.customerPrice.max !== undefined) {
        params.set('customerPrice.max', filters.customerPrice.max.toString())
      }
    }

    if (filters.quantity) {
      params.set('quantity.operator', filters.quantity.operator)
      if (filters.quantity.value !== undefined) {
        params.set('quantity.value', filters.quantity.value.toString())
      }
      if (filters.quantity.min !== undefined) {
        params.set('quantity.min', filters.quantity.min.toString())
      }
      if (filters.quantity.max !== undefined) {
        params.set('quantity.max', filters.quantity.max.toString())
      }
    }

    // Date filters
    if (filters.createdAt) {
      params.set('createdAt.operator', filters.createdAt.operator)
      if (filters.createdAt.value) {
        const dateValue = filters.createdAt.value instanceof Date
          ? filters.createdAt.value.toISOString()
          : filters.createdAt.value
        params.set('createdAt.value', dateValue)
      }
      if (filters.createdAt.startDate) {
        const startDate = filters.createdAt.startDate instanceof Date
          ? filters.createdAt.startDate.toISOString()
          : filters.createdAt.startDate
        params.set('createdAt.startDate', startDate)
      }
      if (filters.createdAt.endDate) {
        const endDate = filters.createdAt.endDate instanceof Date
          ? filters.createdAt.endDate.toISOString()
          : filters.createdAt.endDate
        params.set('createdAt.endDate', endDate)
      }
    }

    if (filters.updatedAt) {
      params.set('updatedAt.operator', filters.updatedAt.operator)
      if (filters.updatedAt.value) {
        const dateValue = filters.updatedAt.value instanceof Date
          ? filters.updatedAt.value.toISOString()
          : filters.updatedAt.value
        params.set('updatedAt.value', dateValue)
      }
      if (filters.updatedAt.startDate) {
        const startDate = filters.updatedAt.startDate instanceof Date
          ? filters.updatedAt.startDate.toISOString()
          : filters.updatedAt.startDate
        params.set('updatedAt.startDate', startDate)
      }
      if (filters.updatedAt.endDate) {
        const endDate = filters.updatedAt.endDate instanceof Date
          ? filters.updatedAt.endDate.toISOString()
          : filters.updatedAt.endDate
        params.set('updatedAt.endDate', endDate)
      }
    }

    // Entity filters
    if (filters.customers) {
      if (filters.customers.include.length > 0) {
        params.set('customers.include', filters.customers.include.join(','))
      }
      if (filters.customers.exclude.length > 0) {
        params.set('customers.exclude', filters.customers.exclude.join(','))
      }
      if (filters.customers.includeNull) {
        params.set('customers.includeNull', 'true')
      }
    }

    if (filters.storeCodes) {
      if (filters.storeCodes.include.length > 0) {
        params.set('storeCodes.include', filters.storeCodes.include.join(','))
      }
      if (filters.storeCodes.exclude.length > 0) {
        params.set('storeCodes.exclude', filters.storeCodes.exclude.join(','))
      }
      if (filters.storeCodes.includeNull) {
        params.set('storeCodes.includeNull', 'true')
      }
    }

    // Sort parameters
    if (sort.columns.length > 0) {
      sort.columns.forEach((column, index) => {
        params.set(`sort.${index}.field`, column.field)
        params.set(`sort.${index}.direction`, column.direction)
        params.set(`sort.${index}.priority`, column.priority.toString())
      })
    }

    // Pagination parameters
    params.set('page', paginationConfig.page.toString())
    params.set('limit', paginationConfig.limit.toString())

    return params.toString()
  }, [currentPagination])

  const applyFilters = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Build query parameters using current filters, sort, and pagination
      const queryParams = buildQueryParams(filters, sort, currentPagination)

      // Update URL
      const newUrl = queryParams ? `?${queryParams}` : ''
      router.replace(newUrl, { scroll: false })

      // Fetch data
      const response = await fetch(`/api/orders?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache'
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to fetch orders: ${response.status} ${response.statusText} - ${errorText}`)
      }

      const data = await response.json()

      // Handle both legacy and advanced response formats
      if (Array.isArray(data)) {
        // Legacy format
        setItems(data)
        setPagination({
          ...defaultPagination,
          total: data.length
        })
      } else {
        // Advanced format
        const filterResponse = data as FilterResponse<unknown>
        setItems(filterResponse.data)
        setPagination(filterResponse.pagination)
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setItems([])
      setPagination(defaultPagination)
    } finally {
      setIsLoading(false)
    }
  }, [filters, sort, currentPagination, router, buildQueryParams])

  const updateFilter = useCallback((key: keyof FilterConfig, value: unknown) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    // Reset to page 1 when filters change
    setCurrentPagination(prev => ({ ...prev, page: 1 }))
  }, [])

  const updateSort = useCallback((newSort: SortConfig) => {
    setSort(newSort)
    // Reset to page 1 when sort changes
    setCurrentPagination(prev => ({ ...prev, page: 1 }))
  }, [])

  const updatePagination = useCallback((page: number, limit?: number) => {
    setCurrentPagination(prev => ({
      ...prev,
      page,
      ...(limit && { limit })
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({})
    setSort(defaultSort)
    setCurrentPagination(defaultPagination)
    router.replace('', { scroll: false })
  }, [router])

  return {
    // Filter state
    filters,
    sort,
    isLoading,
    error,

    // Data
    items,
    pagination,

    // Actions
    updateFilter,
    updateSort,
    updatePagination,
    clearFilters,
    applyFilters,


  }
}
