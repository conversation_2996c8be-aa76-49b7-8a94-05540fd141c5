import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest, User } from './auth'

export interface AuthenticatedRequest extends NextRequest {
  user?: User
}

export interface PermissionCheck {
  resource: string
  action: string
}

/**
 * Authentication middleware - verifies JW<PERSON> token and adds user to request
 */
export function withAuth(handler: (req: AuthenticatedRequest) => Promise<NextResponse>) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      const user = await getUserFromRequest(req)
      
      if (!user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      if (!user.isActive) {
        return NextResponse.json(
          { error: 'Account disabled' },
          { status: 403 }
        )
      }

      // Add user to request
      const authenticatedReq = req as AuthenticatedRequest
      authenticatedReq.user = user

      return handler(authenticatedReq)
    } catch (error) {
      console.error('Authentication middleware error:', error)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      )
    }
  }
}

/**
 * Authorization middleware - checks user permissions
 */
export function withPermission(
  permission: PermissionCheck | PermissionCheck[],
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(async (req: AuthenticatedRequest): Promise<NextResponse> => {
    try {
      const user = req.user!
      const permissions = Array.isArray(permission) ? permission : [permission]

      // Get user permissions
      const userPermissions = user.roles?.flatMap(userRole => 
        userRole.role.permissions.map(rolePermission => ({
          resource: rolePermission.permission.resource,
          action: rolePermission.permission.action
        }))
      ) || []

      // Check if user has required permissions
      const hasPermission = permissions.every(perm => 
        userPermissions.some(userPerm => 
          userPerm.resource === perm.resource && userPerm.action === perm.action
        )
      )

      if (!hasPermission) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      return handler(req)
    } catch (error) {
      console.error('Authorization middleware error:', error)
      return NextResponse.json(
        { error: 'Authorization failed' },
        { status: 403 }
      )
    }
  })
}

/**
 * Role-based authorization middleware
 */
export function withRole(
  roles: string | string[],
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(async (req: AuthenticatedRequest): Promise<NextResponse> => {
    try {
      const user = req.user!
      const requiredRoles = Array.isArray(roles) ? roles : [roles]

      // Get user roles
      const userRoles = user.roles?.map(userRole => userRole.role.name) || []

      // Check if user has required roles
      const hasRole = requiredRoles.some(role => userRoles.includes(role))

      if (!hasRole) {
        return NextResponse.json(
          { error: 'Insufficient role permissions' },
          { status: 403 }
        )
      }

      return handler(req)
    } catch (error) {
      console.error('Role authorization middleware error:', error)
      return NextResponse.json(
        { error: 'Role authorization failed' },
        { status: 403 }
      )
    }
  })
}

/**
 * Admin-only middleware
 */
export function withAdmin(handler: (req: AuthenticatedRequest) => Promise<NextResponse>) {
  return withRole('admin', handler)
}

/**
 * Optional authentication middleware - adds user to request if authenticated
 */
export function withOptionalAuth(handler: (req: AuthenticatedRequest) => Promise<NextResponse>) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      const user = await getUserFromRequest(req)
      
      // Add user to request (can be null)
      const authenticatedReq = req as AuthenticatedRequest
      authenticatedReq.user = user || undefined

      return handler(authenticatedReq)
    } catch (error) {
      console.error('Optional authentication middleware error:', error)
      
      // Continue without user if authentication fails
      const authenticatedReq = req as AuthenticatedRequest
      authenticatedReq.user = undefined

      return handler(authenticatedReq)
    }
  }
}

/**
 * Rate limiting middleware
 */
interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (req: NextRequest) => string // Custom key generator
}

const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function withRateLimit(
  config: RateLimitConfig,
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      const key = config.keyGenerator 
        ? config.keyGenerator(req)
        : req.ip || req.headers.get('x-forwarded-for') || 'anonymous'

      const now = Date.now()
      const windowStart = now - config.windowMs

      // Clean up old entries
      for (const [k, v] of rateLimitStore.entries()) {
        if (v.resetTime < windowStart) {
          rateLimitStore.delete(k)
        }
      }

      // Get current count for this key
      const current = rateLimitStore.get(key)
      
      if (!current) {
        // First request in window
        rateLimitStore.set(key, { count: 1, resetTime: now + config.windowMs })
      } else if (current.resetTime < now) {
        // Window expired, reset
        rateLimitStore.set(key, { count: 1, resetTime: now + config.windowMs })
      } else if (current.count >= config.maxRequests) {
        // Rate limit exceeded
        const resetIn = Math.ceil((current.resetTime - now) / 1000)
        
        return NextResponse.json(
          { 
            error: 'Rate limit exceeded',
            message: `Too many requests. Try again in ${resetIn} seconds.`,
            retryAfter: resetIn
          },
          { 
            status: 429,
            headers: {
              'Retry-After': resetIn.toString(),
              'X-RateLimit-Limit': config.maxRequests.toString(),
              'X-RateLimit-Remaining': '0',
              'X-RateLimit-Reset': current.resetTime.toString()
            }
          }
        )
      } else {
        // Increment count
        current.count++
      }

      // Add rate limit headers
      const remaining = Math.max(0, config.maxRequests - (current?.count || 0))
      const response = await handler(req)
      
      response.headers.set('X-RateLimit-Limit', config.maxRequests.toString())
      response.headers.set('X-RateLimit-Remaining', remaining.toString())
      response.headers.set('X-RateLimit-Reset', (current?.resetTime || now + config.windowMs).toString())

      return response
    } catch (error) {
      console.error('Rate limiting middleware error:', error)
      return handler(req) // Continue without rate limiting on error
    }
  }
}

/**
 * CORS middleware
 */
interface CORSConfig {
  origin?: string | string[] | boolean
  methods?: string[]
  allowedHeaders?: string[]
  credentials?: boolean
}

export function withCORS(
  config: CORSConfig = {},
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const response = await handler(req)

    // Set CORS headers
    const origin = req.headers.get('origin')
    
    if (config.origin === true) {
      response.headers.set('Access-Control-Allow-Origin', '*')
    } else if (typeof config.origin === 'string') {
      response.headers.set('Access-Control-Allow-Origin', config.origin)
    } else if (Array.isArray(config.origin) && origin && config.origin.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin)
    }

    if (config.methods) {
      response.headers.set('Access-Control-Allow-Methods', config.methods.join(', '))
    }

    if (config.allowedHeaders) {
      response.headers.set('Access-Control-Allow-Headers', config.allowedHeaders.join(', '))
    }

    if (config.credentials) {
      response.headers.set('Access-Control-Allow-Credentials', 'true')
    }

    return response
  }
}

/**
 * Security headers middleware
 */
export function withSecurityHeaders(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const response = await handler(req)

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')

    return response
  }
}

/**
 * Combine multiple middlewares
 */
export function combineMiddleware(
  ...middlewares: Array<(handler: any) => any>
) {
  return (handler: any) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler)
  }
}
