import { PrismaClient } from '@prisma/client'
import { 
  PasswordUtils, 
  SessionUtils, 
  AuthResult, 
  RegisterData, 
  LoginData, 
  User 
} from './auth'
import crypto from 'crypto'

const prisma = new PrismaClient()

export class AuthService {
  /**
   * Register a new user
   */
  static async register(data: RegisterData): Promise<AuthResult> {
    try {
      // Validate password
      const passwordValidation = PasswordUtils.validate(data.password)
      if (!passwordValidation.valid) {
        return {
          success: false,
          error: 'INVALID_PASSWORD',
          message: passwordValidation.errors.join(', ')
        }
      }

      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: data.email.toLowerCase() },
            ...(data.username ? [{ username: data.username }] : [])
          ]
        }
      })

      if (existingUser) {
        return {
          success: false,
          error: 'USER_EXISTS',
          message: existingUser.email === data.email.toLowerCase() 
            ? '<PERSON><PERSON> already registered' 
            : 'Username already taken'
        }
      }

      // Hash password
      const passwordHash = await PasswordUtils.hash(data.password)

      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex')

      // Create user
      const user = await prisma.user.create({
        data: {
          email: data.email.toLowerCase(),
          username: data.username || null,
          firstName: data.firstName || null,
          lastName: data.lastName || null,
          passwordHash,
          emailVerificationToken,
          phone: data.phone || null,
          timezone: data.timezone || 'Asia/Manila',
          language: data.language || 'en',
          emailVerified: false, // Require email verification
          isActive: true
        }
      })

      // Assign default buyer role
      const buyerRole = await prisma.role.findUnique({
        where: { name: 'buyer' }
      })

      if (buyerRole) {
        await prisma.userRole.create({
          data: {
            userId: user.id,
            roleId: buyerRole.id,
            assignedAt: new Date(),
            isActive: true
          }
        })
      }

      // Create audit log
      await this.createAuditLog({
        userId: user.id,
        action: 'USER_REGISTERED',
        resource: 'users',
        resourceId: user.id.toString(),
        newValues: {
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        }
      })

      return {
        success: true,
        user: this.sanitizeUser(user),
        message: 'Registration successful. Please check your email to verify your account.'
      }
    } catch (error) {
      console.error('Registration error:', error)
      return {
        success: false,
        error: 'REGISTRATION_FAILED',
        message: 'Registration failed. Please try again.'
      }
    }
  }

  /**
   * Login user
   */
  static async login(data: LoginData, ipAddress?: string, userAgent?: string): Promise<AuthResult> {
    try {
      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email: data.email.toLowerCase() },
        include: {
          roles: {
            where: { isActive: true },
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!user) {
        return {
          success: false,
          error: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      }

      // Check if user is active
      if (!user.isActive) {
        return {
          success: false,
          error: 'ACCOUNT_DISABLED',
          message: 'Your account has been disabled. Please contact support.'
        }
      }

      // Check for account lockout
      if (user.lockoutUntil && user.lockoutUntil > new Date()) {
        const remainingTime = Math.ceil((user.lockoutUntil.getTime() - Date.now()) / 60000)
        return {
          success: false,
          error: 'ACCOUNT_LOCKED',
          message: `Account locked. Try again in ${remainingTime} minutes.`
        }
      }

      // Verify password
      const isValidPassword = await PasswordUtils.verify(data.password, user.passwordHash)
      
      if (!isValidPassword) {
        // Increment login attempts
        const newAttempts = user.loginAttempts + 1
        const lockoutUntil = newAttempts >= 5 ? new Date(Date.now() + 15 * 60 * 1000) : null // 15 minutes lockout

        await prisma.user.update({
          where: { id: user.id },
          data: {
            loginAttempts: newAttempts,
            lockoutUntil
          }
        })

        return {
          success: false,
          error: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      }

      // Reset login attempts on successful login
      await prisma.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: 0,
          lockoutUntil: null
        }
      })

      // Create session
      const { token, refreshToken } = await SessionUtils.create(user.id, ipAddress, userAgent)

      // Create audit log
      await this.createAuditLog({
        userId: user.id,
        action: 'USER_LOGIN',
        resource: 'users',
        resourceId: user.id.toString(),
        ipAddress,
        userAgent
      })

      return {
        success: true,
        user: this.sanitizeUser(user),
        token,
        refreshToken,
        message: 'Login successful'
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'LOGIN_FAILED',
        message: 'Login failed. Please try again.'
      }
    }
  }

  /**
   * Logout user
   */
  static async logout(sessionId: string, userId?: number): Promise<AuthResult> {
    try {
      await SessionUtils.invalidate(sessionId)

      if (userId) {
        await this.createAuditLog({
          userId,
          action: 'USER_LOGOUT',
          resource: 'users',
          resourceId: userId.toString()
        })
      }

      return {
        success: true,
        message: 'Logout successful'
      }
    } catch (error) {
      console.error('Logout error:', error)
      return {
        success: false,
        error: 'LOGOUT_FAILED',
        message: 'Logout failed'
      }
    }
  }

  /**
   * Verify email
   */
  static async verifyEmail(token: string): Promise<AuthResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { emailVerificationToken: token }
      })

      if (!user) {
        return {
          success: false,
          error: 'INVALID_TOKEN',
          message: 'Invalid verification token'
        }
      }

      await prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: true,
          emailVerificationToken: null
        }
      })

      await this.createAuditLog({
        userId: user.id,
        action: 'EMAIL_VERIFIED',
        resource: 'users',
        resourceId: user.id.toString()
      })

      return {
        success: true,
        message: 'Email verified successfully'
      }
    } catch (error) {
      console.error('Email verification error:', error)
      return {
        success: false,
        error: 'VERIFICATION_FAILED',
        message: 'Email verification failed'
      }
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<AuthResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
      })

      if (!user) {
        // Don't reveal if email exists
        return {
          success: true,
          message: 'If the email exists, a password reset link has been sent.'
        }
      }

      const resetToken = crypto.randomBytes(32).toString('hex')
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken: resetToken,
          passwordResetExpires: resetExpires
        }
      })

      await this.createAuditLog({
        userId: user.id,
        action: 'PASSWORD_RESET_REQUESTED',
        resource: 'users',
        resourceId: user.id.toString()
      })

      // TODO: Send email with reset link
      // await EmailService.sendPasswordReset(user.email, resetToken)

      return {
        success: true,
        message: 'If the email exists, a password reset link has been sent.'
      }
    } catch (error) {
      console.error('Password reset request error:', error)
      return {
        success: false,
        error: 'RESET_REQUEST_FAILED',
        message: 'Password reset request failed'
      }
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(token: string, newPassword: string): Promise<AuthResult> {
    try {
      const passwordValidation = PasswordUtils.validate(newPassword)
      if (!passwordValidation.valid) {
        return {
          success: false,
          error: 'INVALID_PASSWORD',
          message: passwordValidation.errors.join(', ')
        }
      }

      const user = await prisma.user.findFirst({
        where: {
          passwordResetToken: token,
          passwordResetExpires: {
            gt: new Date()
          }
        }
      })

      if (!user) {
        return {
          success: false,
          error: 'INVALID_TOKEN',
          message: 'Invalid or expired reset token'
        }
      }

      const passwordHash = await PasswordUtils.hash(newPassword)

      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordHash,
          passwordResetToken: null,
          passwordResetExpires: null,
          loginAttempts: 0,
          lockoutUntil: null
        }
      })

      // Invalidate all sessions
      await SessionUtils.invalidateAll(user.id)

      await this.createAuditLog({
        userId: user.id,
        action: 'PASSWORD_RESET',
        resource: 'users',
        resourceId: user.id.toString()
      })

      return {
        success: true,
        message: 'Password reset successful'
      }
    } catch (error) {
      console.error('Password reset error:', error)
      return {
        success: false,
        error: 'RESET_FAILED',
        message: 'Password reset failed'
      }
    }
  }

  /**
   * Create audit log entry
   */
  private static async createAuditLog(data: {
    userId?: number
    action: string
    resource: string
    resourceId?: string
    oldValues?: any
    newValues?: any
    ipAddress?: string
    userAgent?: string
  }): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: data.userId || null,
          action: data.action,
          resource: data.resource,
          resourceId: data.resourceId || null,
          oldValues: data.oldValues || null,
          newValues: data.newValues || null,
          ipAddress: data.ipAddress || null,
          userAgent: data.userAgent || null
        }
      })
    } catch (error) {
      console.error('Failed to create audit log:', error)
    }
  }

  /**
   * Sanitize user data for client
   */
  private static sanitizeUser(user: any): User {
    const { passwordHash, emailVerificationToken, passwordResetToken, ...sanitized } = user
    return sanitized
  }
}
