import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify } from 'jose'
import { NextRequest } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// JWT Configuration
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'
)
const JWT_ISSUER = 'pasabuy-local'
const JWT_AUDIENCE = 'pasabuy-users'

// Password Configuration
const SALT_ROUNDS = 12
const PASSWORD_MIN_LENGTH = 8

// Session Configuration
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
const REFRESH_TOKEN_DURATION = 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds

export interface User {
  id: number
  email: string
  username?: string | null
  firstName?: string | null
  lastName?: string | null
  emailVerified: boolean
  isActive: boolean
  profilePicture?: string | null
  phone?: string | null
  timezone: string
  language: string
  twoFactorEnabled: boolean
  lastLoginAt?: Date | null
  createdAt: Date
  updatedAt: Date
  roles?: UserRole[]
}

export interface UserRole {
  id: number
  role: {
    id: number
    name: string
    displayName: string
    permissions: RolePermission[]
  }
  isActive: boolean
  expiresAt?: Date | null
}

export interface RolePermission {
  permission: {
    id: number
    name: string
    resource: string
    action: string
  }
}

export interface JWTPayload {
  userId: number
  email: string
  username?: string
  roles: string[]
  permissions: string[]
  sessionId: string
  iat: number
  exp: number
  iss: string
  aud: string
}

export interface AuthResult {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  error?: string
  message?: string
}

export interface RegisterData {
  email: string
  username?: string
  firstName?: string
  lastName?: string
  password: string
  phone?: string
  timezone?: string
  language?: string
}

export interface LoginData {
  email: string
  password: string
  rememberMe?: boolean
}

/**
 * Password utilities
 */
export class PasswordUtils {
  static async hash(password: string): Promise<string> {
    return bcrypt.hash(password, SALT_ROUNDS)
  }

  static async verify(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  static validate(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (password.length < PASSWORD_MIN_LENGTH) {
      errors.push(`Password must be at least ${PASSWORD_MIN_LENGTH} characters long`)
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

/**
 * JWT utilities
 */
export class JWTUtils {
  static async sign(payload: Omit<JWTPayload, 'iat' | 'exp' | 'iss' | 'aud'>): Promise<string> {
    return new SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('7d')
      .setIssuer(JWT_ISSUER)
      .setAudience(JWT_AUDIENCE)
      .sign(JWT_SECRET)
  }

  static async verify(token: string): Promise<JWTPayload | null> {
    try {
      const { payload } = await jwtVerify(token, JWT_SECRET, {
        issuer: JWT_ISSUER,
        audience: JWT_AUDIENCE,
      })
      return payload as JWTPayload
    } catch (error) {
      console.error('JWT verification failed:', error)
      return null
    }
  }

  static async refresh(refreshToken: string): Promise<{ token: string; refreshToken: string } | null> {
    try {
      // Verify the refresh token and get session
      const session = await prisma.userSession.findUnique({
        where: { refreshToken, isActive: true },
        include: {
          user: {
            include: {
              roles: {
                where: { isActive: true },
                include: {
                  role: {
                    include: {
                      permissions: {
                        include: {
                          permission: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!session || session.expiresAt < new Date()) {
        return null
      }

      // Generate new tokens
      const roles = session.user.roles.map(ur => ur.role.name)
      const permissions = session.user.roles.flatMap(ur => 
        ur.role.permissions.map(rp => rp.permission.name)
      )

      const newToken = await JWTUtils.sign({
        userId: session.user.id,
        email: session.user.email,
        username: session.user.username || undefined,
        roles,
        permissions,
        sessionId: session.id
      })

      const newRefreshToken = await JWTUtils.generateRefreshToken()

      // Update session
      await prisma.userSession.update({
        where: { id: session.id },
        data: {
          token: newToken,
          refreshToken: newRefreshToken,
          expiresAt: new Date(Date.now() + SESSION_DURATION),
          updatedAt: new Date()
        }
      })

      return {
        token: newToken,
        refreshToken: newRefreshToken
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      return null
    }
  }

  static async generateRefreshToken(): Promise<string> {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }
}

/**
 * Session utilities
 */
export class SessionUtils {
  static async create(userId: number, ipAddress?: string, userAgent?: string): Promise<{ token: string; refreshToken: string; sessionId: string }> {
    // Get user with roles and permissions
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        roles: {
          where: { isActive: true },
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    // Generate tokens
    const refreshToken = await JWTUtils.generateRefreshToken()
    const sessionId = crypto.randomUUID()

    // Create session record
    const session = await prisma.userSession.create({
      data: {
        id: sessionId,
        userId,
        token: '', // Will be updated after JWT creation
        refreshToken,
        expiresAt: new Date(Date.now() + SESSION_DURATION),
        ipAddress,
        userAgent,
        isActive: true
      }
    })

    // Generate JWT with session info
    const roles = user.roles.map(ur => ur.role.name)
    const permissions = user.roles.flatMap(ur => 
      ur.role.permissions.map(rp => rp.permission.name)
    )

    const token = await JWTUtils.sign({
      userId: user.id,
      email: user.email,
      username: user.username || undefined,
      roles,
      permissions,
      sessionId: session.id
    })

    // Update session with token
    await prisma.userSession.update({
      where: { id: session.id },
      data: { token }
    })

    // Update user last login
    await prisma.user.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() }
    })

    return {
      token,
      refreshToken,
      sessionId: session.id
    }
  }

  static async invalidate(sessionId: string): Promise<void> {
    await prisma.userSession.update({
      where: { id: sessionId },
      data: { isActive: false }
    })
  }

  static async invalidateAll(userId: number): Promise<void> {
    await prisma.userSession.updateMany({
      where: { userId },
      data: { isActive: false }
    })
  }

  static async cleanup(): Promise<void> {
    // Remove expired sessions
    await prisma.userSession.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    })
  }
}

/**
 * Extract user from request
 */
export async function getUserFromRequest(request: NextRequest): Promise<User | null> {
  try {
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return null
    }

    const payload = await JWTUtils.verify(token)
    if (!payload) {
      return null
    }

    // Verify session is still active
    const session = await prisma.userSession.findUnique({
      where: { 
        id: payload.sessionId,
        isActive: true
      }
    })

    if (!session || session.expiresAt < new Date()) {
      return null
    }

    // Get user with roles
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        roles: {
          where: { isActive: true },
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    })

    return user
  } catch (error) {
    console.error('Error extracting user from request:', error)
    return null
  }
}
