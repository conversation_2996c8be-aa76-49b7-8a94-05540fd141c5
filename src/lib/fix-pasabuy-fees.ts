import { prisma } from './db'

async function fixPasabuyFees() {
  console.log('🔄 Fixing Pasabuy fees for existing tiers...')
  
  try {
    // Update all store pricing tiers with proper Pasabuy fees
    const storeTiers = await prisma.storePricingTier.findMany({
      orderBy: [{ storePricingId: 'asc' }, { sortOrder: 'asc' }]
    })

    console.log(`Found ${storeTiers.length} store pricing tiers to update`)

    for (const tier of storeTiers) {
      let pasabuyFee = 25 // default
      
      // Assign Pasabuy fee based on price range
      if (tier.maxPrice !== null && tier.maxPrice <= 200) {
        pasabuyFee = 15 // Lower fee for low-price items (₱0-₱200)
      } else if (tier.minPrice > 500) {
        pasabuyFee = 35 // Higher fee for expensive items (₱500+)
      } else {
        pasabuyFee = 25 // Standard fee for mid-range items (₱200-₱500)
      }

      await prisma.storePricingTier.update({
        where: { id: tier.id },
        data: { pasabuy<PERSON>ee }
      })

      console.log(`✅ Updated store tier ${tier.id}: ₱${tier.minPrice}-${tier.maxPrice || '∞'} → Pasabuy fee: ₱${pasabuyFee}`)
    }

    // Update all default pricing tiers
    const defaultTiers = await prisma.defaultPricingTier.findMany()
    
    console.log(`Found ${defaultTiers.length} default pricing tiers to update`)

    for (const tier of defaultTiers) {
      await prisma.defaultPricingTier.update({
        where: { id: tier.id },
        data: { pasabuyFee: 20 } // Standard default Pasabuy fee
      })
      console.log(`✅ Updated default tier ${tier.id} with ₱20 Pasabuy fee`)
    }

    console.log('🎉 All Pasabuy fees updated successfully!')
    
    // Verify the updates
    console.log('\n📊 Verification:')
    const updatedStoreTiers = await prisma.storePricingTier.findMany({
      where: { storePricingId: 7 }, // Check store 7 specifically
      orderBy: { sortOrder: 'asc' }
    })
    
    console.log('Store 7 tiers:')
    updatedStoreTiers.forEach(tier => {
      console.log(`  Tier: ₱${tier.minPrice}-${tier.maxPrice || '∞'} → Markup: ₱${tier.markupValue}, Pasabuy Fee: ₱${tier.pasabuyFee}`)
    })
    
  } catch (error) {
    console.error('❌ Update failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

fixPasabuyFees()
