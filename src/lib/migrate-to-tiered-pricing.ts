import { prisma } from './db'

/**
 * Migration script to convert existing pricing configurations to tiered structure
 * This script should be run once after updating the database schema
 */
export async function migrateToTieredPricing() {
  console.log('🔄 Starting migration to tiered pricing system...')
  
  try {
    // Check if default pricing exists and has no tiers
    const defaultPricing = await prisma.defaultPricing.findFirst({
      where: { isActive: true },
      include: { pricingTiers: true }
    })

    if (defaultPricing && defaultPricing.pricingTiers.length === 0) {
      console.log('📝 Creating default pricing tier...')
      
      // Create a single tier that covers all prices with 100% markup
      await prisma.defaultPricingTier.create({
        data: {
          defaultPricingId: defaultPricing.id,
          minPrice: 0,
          maxPrice: null, // "and above"
          markupType: 'PERCENTAGE',
          markupValue: 100.00, // 100% markup
          sortOrder: 0
        }
      })
      
      console.log('✅ Default pricing tier created')
    }

    // Check store pricing configurations
    const storePricings = await prisma.storePricing.findMany({
      include: { pricingTiers: true }
    })

    for (const storePricing of storePricings) {
      if (storePricing.pricingTiers.length === 0) {
        console.log(`📝 Creating pricing tiers for store ${storePricing.name}...`)
        
        // Create example tiered pricing based on store
        const tiers = [
          {
            minPrice: 0,
            maxPrice: 200,
            markupType: 'FIXED_AMOUNT',
            markupValue: 70,
            sortOrder: 0
          },
          {
            minPrice: 200.01,
            maxPrice: 500,
            markupType: 'FIXED_AMOUNT',
            markupValue: 100,
            sortOrder: 1
          },
          {
            minPrice: 500.01,
            maxPrice: null, // "and above"
            markupType: 'FIXED_AMOUNT',
            markupValue: 150,
            sortOrder: 2
          }
        ]

        for (const tier of tiers) {
          await prisma.storePricingTier.create({
            data: {
              storePricingId: storePricing.id,
              ...tier
            }
          })
        }
        
        console.log(`✅ Created ${tiers.length} pricing tiers for ${storePricing.name}`)
      }
    }

    console.log('🎉 Migration to tiered pricing completed successfully!')
    
    // Display summary
    const summary = await getMigrationSummary()
    console.log('\n📊 Migration Summary:')
    console.log(`- Default pricing configurations: ${summary.defaultPricingCount}`)
    console.log(`- Default pricing tiers: ${summary.defaultTierCount}`)
    console.log(`- Store pricing configurations: ${summary.storePricingCount}`)
    console.log(`- Store pricing tiers: ${summary.storeTierCount}`)
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  }
}

async function getMigrationSummary() {
  const defaultPricingCount = await prisma.defaultPricing.count()
  const defaultTierCount = await prisma.defaultPricingTier.count()
  const storePricingCount = await prisma.storePricing.count()
  const storeTierCount = await prisma.storePricingTier.count()
  
  return {
    defaultPricingCount,
    defaultTierCount,
    storePricingCount,
    storeTierCount
  }
}

/**
 * Seed sample tiered pricing configurations for demonstration
 */
export async function seedTieredPricingExamples() {
  console.log('🌱 Seeding tiered pricing examples...')
  
  try {
    // Get some stores to create examples for
    const stores = await prisma.storeCode.findMany({
      take: 2,
      where: {
        NOT: {
          pricing: {
            isNot: null
          }
        }
      }
    })

    for (const store of stores) {
      console.log(`Creating tiered pricing for store: ${store.code}`)
      
      const storePricing = await prisma.storePricing.create({
        data: {
          storeCodeId: store.id,
          name: `${store.code} Tiered Pricing`,
          serviceFee: 25.00,
          isActive: true,
          pricingTiers: {
            create: [
              {
                minPrice: 0,
                maxPrice: 100,
                markupType: 'FIXED_AMOUNT',
                markupValue: 50,
                sortOrder: 0
              },
              {
                minPrice: 100.01,
                maxPrice: 300,
                markupType: 'FIXED_AMOUNT',
                markupValue: 80,
                sortOrder: 1
              },
              {
                minPrice: 300.01,
                maxPrice: 500,
                markupType: 'PERCENTAGE',
                markupValue: 30, // 30% markup
                sortOrder: 2
              },
              {
                minPrice: 500.01,
                maxPrice: null, // "and above"
                markupType: 'PERCENTAGE',
                markupValue: 25, // 25% markup for expensive items
                sortOrder: 3
              }
            ]
          }
        },
        include: {
          pricingTiers: true
        }
      })
      
      console.log(`✅ Created ${storePricing.pricingTiers.length} tiers for ${store.code}`)
    }
    
    console.log('🎉 Tiered pricing examples seeded successfully!')
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    throw error
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateToTieredPricing()
    .then(() => seedTieredPricingExamples())
    .then(() => {
      console.log('🏁 All done!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error:', error)
      process.exit(1)
    })
}
