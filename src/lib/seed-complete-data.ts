import { prisma } from './db'

async function seedCompleteData() {
  console.log('🌱 Seeding complete data with stores and tiered pricing...')
  
  try {
    // Create sample store codes
    console.log('📝 Creating store codes...')
    const stores = await Promise.all([
      prisma.storeCode.create({
        data: {
          code: 'AYALA',
          name: 'Ayala Malls',
          storeType: 'PHYSICAL',
          status: 'ACTIVE',
          city: 'Manila',
          isActive: true
        }
      }),
      prisma.storeCode.create({
        data: {
          code: 'SM',
          name: 'SM Supermalls',
          storeType: 'PHYSICAL',
          status: 'ACTIVE',
          city: 'Quezon City',
          isActive: true
        }
      }),
      prisma.storeCode.create({
        data: {
          code: 'RO<PERSON>NSON<PERSON>',
          name: 'Robinsons Malls',
          storeType: 'PHYSICAL',
          status: 'ACTIVE',
          city: 'Makati',
          isActive: true
        }
      }),
      prisma.storeCode.create({
        data: {
          code: 'LAZAD<PERSON>',
          name: 'Lazada Online',
          storeType: 'ONLINE',
          status: 'ACTIVE',
          website: 'https://lazada.com.ph',
          isActive: true
        }
      }),
      prisma.storeCode.create({
        data: {
          code: 'SHOPEE',
          name: 'Shopee Online',
          storeType: 'ONLINE',
          status: 'ACTIVE',
          website: 'https://shopee.ph',
          isActive: true
        }
      })
    ])

    console.log(`✅ Created ${stores.length} store codes`)

    // Create tiered pricing for each store
    for (const store of stores) {
      console.log(`📝 Creating tiered pricing for ${store.code}...`)
      
      let tiers
      if (store.code === 'AYALA') {
        // Premium store - higher markups, higher Pasabuy fees
        tiers = [
          {
            minPrice: 0,
            maxPrice: 200,
            markupType: 'FIXED_AMOUNT',
            markupValue: 80,
            pasabuyFee: 20,
            sortOrder: 0
          },
          {
            minPrice: 200.01,
            maxPrice: 500,
            markupType: 'FIXED_AMOUNT',
            markupValue: 120,
            pasabuyFee: 30,
            sortOrder: 1
          },
          {
            minPrice: 500.01,
            maxPrice: null,
            markupType: 'FIXED_AMOUNT',
            markupValue: 180,
            pasabuyFee: 40,
            sortOrder: 2
          }
        ]
      } else if (store.code === 'SM') {
        // Standard store - moderate markups and fees
        tiers = [
          {
            minPrice: 0,
            maxPrice: 200,
            markupType: 'FIXED_AMOUNT',
            markupValue: 70,
            pasabuyFee: 15,
            sortOrder: 0
          },
          {
            minPrice: 200.01,
            maxPrice: 500,
            markupType: 'FIXED_AMOUNT',
            markupValue: 100,
            pasabuyFee: 25,
            sortOrder: 1
          },
          {
            minPrice: 500.01,
            maxPrice: null,
            markupType: 'FIXED_AMOUNT',
            markupValue: 150,
            pasabuyFee: 35,
            sortOrder: 2
          }
        ]
      } else if (store.code.includes('ONLINE') || store.code === 'LAZADA' || store.code === 'SHOPEE') {
        // Online stores - percentage-based pricing
        tiers = [
          {
            minPrice: 0,
            maxPrice: 300,
            markupType: 'PERCENTAGE',
            markupValue: 25, // 25% markup
            pasabuyFee: 10,
            sortOrder: 0
          },
          {
            minPrice: 300.01,
            maxPrice: 1000,
            markupType: 'PERCENTAGE',
            markupValue: 20, // 20% markup
            pasabuyFee: 20,
            sortOrder: 1
          },
          {
            minPrice: 1000.01,
            maxPrice: null,
            markupType: 'PERCENTAGE',
            markupValue: 15, // 15% markup for expensive items
            pasabuyFee: 30,
            sortOrder: 2
          }
        ]
      } else {
        // Default store pricing
        tiers = [
          {
            minPrice: 0,
            maxPrice: 250,
            markupType: 'FIXED_AMOUNT',
            markupValue: 60,
            pasabuyFee: 12,
            sortOrder: 0
          },
          {
            minPrice: 250.01,
            maxPrice: 600,
            markupType: 'FIXED_AMOUNT',
            markupValue: 90,
            pasabuyFee: 22,
            sortOrder: 1
          },
          {
            minPrice: 600.01,
            maxPrice: null,
            markupType: 'FIXED_AMOUNT',
            markupValue: 130,
            pasabuyFee: 32,
            sortOrder: 2
          }
        ]
      }

      const storePricing = await prisma.storePricing.create({
        data: {
          storeCodeId: store.id,
          name: `${store.code} Tiered Pricing`,
          serviceFee: 25.00, // Base service fee (not used in calculations)
          isActive: true,
          pricingTiers: {
            create: tiers
          }
        },
        include: {
          pricingTiers: true
        }
      })
      
      console.log(`✅ Created ${storePricing.pricingTiers.length} tiers for ${store.code}`)
    }

    // Create some sample customers
    console.log('📝 Creating sample customers...')
    const customers = await Promise.all([
      prisma.customer.create({
        data: {
          name: 'Juan Dela Cruz',
          email: '<EMAIL>',
          phone: '+639123456789',
          city: 'Manila',
          customerType: 'INDIVIDUAL',
          status: 'ACTIVE'
        }
      }),
      prisma.customer.create({
        data: {
          name: 'Maria Santos',
          email: '<EMAIL>',
          phone: '+639987654321',
          city: 'Quezon City',
          customerType: 'INDIVIDUAL',
          status: 'ACTIVE'
        }
      }),
      prisma.customer.create({
        data: {
          name: 'ABC Trading Corp',
          email: '<EMAIL>',
          phone: '+632123456789',
          city: 'Makati',
          customerType: 'BUSINESS',
          status: 'ACTIVE'
        }
      })
    ])

    console.log(`✅ Created ${customers.length} sample customers`)

    console.log('🎉 Complete data seeding finished successfully!')
    
    // Display summary
    const summary = await getSeedingSummary()
    console.log('\n📊 Seeding Summary:')
    console.log(`- Store codes: ${summary.storeCount}`)
    console.log(`- Store pricing configurations: ${summary.storePricingCount}`)
    console.log(`- Store pricing tiers: ${summary.storeTierCount}`)
    console.log(`- Default pricing configurations: ${summary.defaultPricingCount}`)
    console.log(`- Default pricing tiers: ${summary.defaultTierCount}`)
    console.log(`- Customers: ${summary.customerCount}`)
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function getSeedingSummary() {
  const storeCount = await prisma.storeCode.count()
  const storePricingCount = await prisma.storePricing.count()
  const storeTierCount = await prisma.storePricingTier.count()
  const defaultPricingCount = await prisma.defaultPricing.count()
  const defaultTierCount = await prisma.defaultPricingTier.count()
  const customerCount = await prisma.customer.count()
  
  return {
    storeCount,
    storePricingCount,
    storeTierCount,
    defaultPricingCount,
    defaultTierCount,
    customerCount
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedCompleteData()
    .then(() => {
      console.log('🏁 Complete seeding finished!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding error:', error)
      process.exit(1)
    })
}
