import { prisma } from '@/lib/db'

export async function seedPricingSettings() {
  try {
    // Check if default pricing already exists
    const existingSettings = await prisma.defaultPricing.count()
    if (existingSettings > 0) {
      console.log('Default pricing settings already exist, skipping seed')
      return
    }

    console.log('Seeding default pricing settings...')

    // Create default pricing settings
    const defaultSettings = await prisma.defaultPricing.create({
      data: {
        markupType: 'PERCENTAGE',
        markupValue: 100.00, // 100% markup (double the price)
        serviceFee: 20.00, // ₱20 service fee
        isActive: true
      }
    })

    console.log('✅ Successfully seeded default pricing settings:')
    console.log(`- Markup Type: ${defaultSettings.markupType}`)
    console.log(`- Markup Value: ${defaultSettings.markupValue}${defaultSettings.markupType === 'PERCENTAGE' ? '%' : ''}`)
    console.log(`- Service Fee: ₱${defaultSettings.serviceFee}`)

    // Create some sample store-specific pricing
    const stores = await prisma.storeCode.findMany({
      take: 3,
      select: { id: true, code: true, name: true }
    })

    if (stores.length > 0) {
      console.log('\nCreating sample store-specific pricing...')

      for (const [index, store] of stores.entries()) {
        const storePricing = await prisma.storePricing.create({
          data: {
            storeCodeId: store.id,
            name: `${store.code} Custom Pricing`,
            markupType: index === 0 ? 'PERCENTAGE' : 'FIXED_AMOUNT',
            markupValue: index === 0 ? 120.00 : 150.00, // 120% or ₱150
            serviceFee: 25.00 + (index * 5), // ₱25, ₱30, ₱35
            isActive: true
          }
        })

        console.log(`✅ Created pricing for store ${store.code}: ${storePricing.markupType === 'PERCENTAGE' ? storePricing.markupValue + '%' : '₱' + storePricing.markupValue} + ₱${storePricing.serviceFee} fee`)
      }
    }

    return defaultSettings
  } catch (error) {
    console.error('Error seeding pricing settings:', error)
    throw error
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedPricingSettings()
    .then(() => {
      console.log('Pricing settings seeded successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Failed to seed pricing settings:', error)
      process.exit(1)
    })
}
