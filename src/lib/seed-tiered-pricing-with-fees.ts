import { prisma } from './db'

async function seedTieredPricingWithFees() {
  console.log('🌱 Seeding tiered pricing with Pasabuy fees...')
  
  try {
    // First, clean up existing pricing configurations
    console.log('🧹 Cleaning up existing pricing configurations...')
    await prisma.storePricingTier.deleteMany()
    await prisma.defaultPricingTier.deleteMany()
    await prisma.storePricing.deleteMany()
    await prisma.defaultPricing.deleteMany()

    // Create default pricing with tiered Pasabuy fees
    console.log('📝 Creating default pricing configuration...')
    const defaultPricing = await prisma.defaultPricing.create({
      data: {
        serviceFee: 20.00, // Base service fee (not used in calculations)
        isActive: true,
        pricingTiers: {
          create: [
            {
              minPrice: 0,
              maxPrice: null, // "and above" - covers all prices
              markupType: 'PERCENTAGE',
              markupValue: 100.00, // 100% markup (double the price)
              pasabuyFee: 20.00, // ₱20 Pasabuy fee for default pricing
              sortOrder: 0
            }
          ]
        }
      },
      include: {
        pricingTiers: true
      }
    })
    console.log(`✅ Created default pricing with ${defaultPricing.pricingTiers.length} tier`)

    // Get some stores to create tiered pricing for
    const stores = await prisma.storeCode.findMany({
      take: 3,
      orderBy: { id: 'asc' }
    })

    for (const store of stores) {
      console.log(`📝 Creating tiered pricing for store: ${store.code}`)
      
      const storePricing = await prisma.storePricing.create({
        data: {
          storeCodeId: store.id,
          name: `${store.code} Tiered Pricing with Pasabuy Fees`,
          serviceFee: 25.00, // Base service fee (not used in calculations)
          isActive: true,
          pricingTiers: {
            create: [
              {
                minPrice: 0,
                maxPrice: 200,
                markupType: 'FIXED_AMOUNT',
                markupValue: 70,
                pasabuyFee: 15, // Lower Pasabuy fee for low-price items
                sortOrder: 0
              },
              {
                minPrice: 200.01,
                maxPrice: 500,
                markupType: 'FIXED_AMOUNT',
                markupValue: 100,
                pasabuyFee: 25, // Standard Pasabuy fee for mid-range items
                sortOrder: 1
              },
              {
                minPrice: 500.01,
                maxPrice: null, // "and above"
                markupType: 'FIXED_AMOUNT',
                markupValue: 150,
                pasabuyFee: 35, // Higher Pasabuy fee for expensive items
                sortOrder: 2
              }
            ]
          }
        },
        include: {
          pricingTiers: true
        }
      })
      
      console.log(`✅ Created ${storePricing.pricingTiers.length} tiers for ${store.code}`)
      
      // Display the tiers
      storePricing.pricingTiers.forEach((tier, index) => {
        const priceRange = tier.maxPrice 
          ? `₱${tier.minPrice} - ₱${tier.maxPrice}`
          : `₱${tier.minPrice}+`
        console.log(`  Tier ${index + 1}: ${priceRange} → ₱${tier.markupValue} markup - ₱${tier.pasabuyFee} Pasabuy fee`)
      })
    }

    console.log('🎉 Tiered pricing with Pasabuy fees seeded successfully!')
    
    // Display summary
    const summary = await getSeedingSummary()
    console.log('\n📊 Seeding Summary:')
    console.log(`- Default pricing configurations: ${summary.defaultPricingCount}`)
    console.log(`- Default pricing tiers: ${summary.defaultTierCount}`)
    console.log(`- Store pricing configurations: ${summary.storePricingCount}`)
    console.log(`- Store pricing tiers: ${summary.storeTierCount}`)
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function getSeedingSummary() {
  const defaultPricingCount = await prisma.defaultPricing.count()
  const defaultTierCount = await prisma.defaultPricingTier.count()
  const storePricingCount = await prisma.storePricing.count()
  const storeTierCount = await prisma.storePricingTier.count()
  
  return {
    defaultPricingCount,
    defaultTierCount,
    storePricingCount,
    storeTierCount
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedTieredPricingWithFees()
    .then(() => {
      console.log('🏁 Seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding error:', error)
      process.exit(1)
    })
}
