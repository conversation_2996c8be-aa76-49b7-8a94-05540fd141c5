import { prisma } from './db'

async function updateExistingTiers() {
  console.log('🔄 Updating existing tiers with Pasabuy fees...')
  
  try {
    // Get all existing tiers that don't have pasabuyFee set
    const tiers = await prisma.storePricingTier.findMany({
      where: {
        OR: [
          { pasabuyFee: 0 },
          { pasabuyFee: null }
        ]
      },
      orderBy: { sortOrder: 'asc' }
    })

    console.log(`Found ${tiers.length} tiers to update`)

    for (const tier of tiers) {
      // Assign Pasabuy fee based on price range
      let pasabuyFee = 25 // default
      
      if (tier.maxPrice !== null && tier.maxPrice <= 200) {
        pasabuyFee = 15 // Lower fee for low-price items
      } else if (tier.minPrice > 500) {
        pasabuyFee = 35 // Higher fee for expensive items
      } else {
        pasabuyFee = 25 // Standard fee for mid-range items
      }

      await prisma.storePricingTier.update({
        where: { id: tier.id },
        data: { pasabuyFee }
      })

      console.log(`Updated tier ${tier.id}: ₱${tier.minPrice}-${tier.maxPrice || '∞'} → Pasabuy fee: ₱${pasabuyFee}`)
    }

    // Also update default pricing tiers
    const defaultTiers = await prisma.defaultPricingTier.findMany({
      where: {
        OR: [
          { pasabuyFee: 0 },
          { pasabuyFee: null }
        ]
      }
    })

    for (const tier of defaultTiers) {
      await prisma.defaultPricingTier.update({
        where: { id: tier.id },
        data: { pasabuyFee: 20 } // Standard default Pasabuy fee
      })
      console.log(`Updated default tier ${tier.id} with ₱20 Pasabuy fee`)
    }

    console.log('✅ All existing tiers updated with Pasabuy fees!')
    
  } catch (error) {
    console.error('❌ Update failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

updateExistingTiers()
